<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SaleListMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SaleListEntity" >
        <result column="sale_list_id" property="saleListId" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="shop_unique" property="shopUnique" />
        <result column="sale_list_datetime" property="saleListDatetime" />
        <result column="sale_list_total" property="saleListTotal" />
        <result column="sale_list_pur" property="saleListPur" />
        <result column="sale_list_totalcount" property="saleListTotalcount" />
        <result column="cus_unique" property="cusUnique" />
        <result column="sale_type" property="saleType" />
        <result column="sale_list_name" property="saleListName" />
        <result column="sale_list_phone" property="saleListPhone" />
        <result column="sale_list_address" property="saleListAddress" />
        <result column="sale_list_delfee" property="saleListDelfee" />
        <result column="shop_subsidy_delfee" property="shopSubsidyDelfee" />
        <result column="sale_list_discount" property="saleListDiscount" />
        <result column="sale_list_state" property="saleListState" />
        <result column="sale_list_handlestate" property="saleListHandlestate" />
        <result column="sale_list_payment" property="saleListPayment" />
        <result column="sale_list_remarks" property="saleListRemarks" />
        <result column="sale_list_flag" property="saleListFlag" />
        <result column="receipt_datetime" property="receiptDatetime" />
        <result column="send_datetime" property="sendDatetime" />
        <result column="sale_list_number" property="saleListNumber" />
        <result column="sale_list_cashier" property="saleListCashier" />
        <result column="sale_list_same_type" property="saleListSameType" />
        <result column="sale_list_actually_received" property="saleListActuallyReceived" />
        <result column="machine_num" property="machineNum" />
        <result column="member_card" property="memberCard" />
        <result column="evaluate_point" property="evaluatePoint" />
        <result column="commission_sum" property="commissionSum" />
        <result column="pay_time" property="payTime" />
        <result column="trade_no" property="tradeNo" />
        <result column="cancle_reason" property="cancleReason" />
        <result column="refund_money" property="refundMoney" />
        <result column="refund_reason" property="refundReason" />
        <result column="refunt_operate_reason" property="refuntOperateReason" />
        <result column="cus_id" property="cusId" />
        <result column="addr_latitude" property="addrLatitude" />
        <result column="addr_longitude" property="addrLongitude" />
        <result column="goods_kind_count" property="goodsKindCount" />
        <result column="shipping_method" property="shippingMethod" />
        <result column="point_deduction" property="pointDeduction" />
        <result column="card_deduction" property="cardDeduction" />
        <result column="label_val" property="labelVal" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="beans_get" property="beansGet" />
        <result column="beans_use" property="beansUse" />
        <result column="shop_coupon_id" property="shopCouponId" />
        <result column="point_val" property="pointVal" />
        <result column="beans_money" property="beansMoney" />
        <result column="points_get" property="pointsGet" />
        <result column="delivery_type" property="deliveryType" />
        <result column="cancel_time" property="cancelTime" />
        <result column="goods_weight" property="goodsWeight" />
        <result column="formid" property="formid" />
        <result column="add_shop_balance_status" property="addShopBalanceStatus" />
        <result column="delivery_error" property="deliveryError" />
        <result column="platform_shop_beans" property="platformShopBeans" />
        <result column="head_image" property="headImage" />
        <result column="cus_face_token" property="cusFaceToken" />
        <result column="sup_give_beans" property="supGiveBeans" />
        <result column="platform_cus_beans" property="platformCusBeans" />
        <result column="return_price" property="returnPrice" />
        <result column="verify_staff_id" property="verifyStaffId" />
        <result column="shop_give_beans" property="shopGiveBeans" />
    </resultMap>

    <sql id="Base_Column_List">
        sale_list_id,
                sale_list_unique,
                shop_unique,
                sale_list_datetime,
                sale_list_total,
                sale_list_pur,
                sale_list_totalcount,
                cus_unique,
                sale_type,
                sale_list_name,
                sale_list_phone,
                sale_list_address,
                sale_list_delfee,
                shop_subsidy_delfee,
                sale_list_discount,
                sale_list_state,
                sale_list_handlestate,
                sale_list_payment,
                sale_list_remarks,
                sale_list_flag,
                receipt_datetime,
                send_datetime,
                sale_list_number,
                sale_list_cashier,
                sale_list_same_type,
                sale_list_actually_received,
                machine_num,
                member_card,
                evaluate_point,
                commission_sum,
                pay_time,
                trade_no,
                cancle_reason,
                refund_money,
                refund_reason,
                refunt_operate_reason,
                cus_id,
                addr_latitude,
                addr_longitude,
                goods_kind_count,
                shipping_method,
                point_deduction,
                card_deduction,
                label_val,
                coupon_amount,
                beans_get,
                beans_use,
                shop_coupon_id,
                point_val,
                beans_money,
                points_get,
                delivery_type,
                cancel_time,
                goods_weight,
                formid,
                add_shop_balance_status,
                delivery_error,
                platform_shop_beans,
                head_image,
                cus_face_token,
                sup_give_beans,
                platform_cus_beans,
                return_price,
                verify_staff_id,
                shop_give_beans
    </sql>

    <!--根据条查询查询-->
    <select id="querySaleListPage" resultType="cc.buyhoo.tax.entity.SaleListEntity" parameterType="map">
        select sale_list_id as saleListId,sale_list_unique as saleListUnique,shop_unique as shopUnique,sale_type as saleType,
               sale_list_name as saleListName,sale_list_total as saleListTotal,sale_list_actually_received as saleListActuallyReceived, trade_no as tradeNo,
               sale_list_datetime as saleListDatetime,sale_list_state as saleListState,sale_list_payment as saleListPayment,shop_coupon_id as shopCouponId,
               coupon_amount as couponAmount
        from sale_list
        where shop_unique=#{shopUnique}
            and sale_list_state=3
            and sale_list_datetime &gt;= #{dateStart}
            and sale_list_datetime &lt; #{dateEnd}
            and sale_list_id > #{saleListId}
        limit #{pageSize}
    </select>
    <select id="selectBuyhooSaleListInfo" resultMap="selectBuyhooInfoDto">
        SELECT
            sl.sale_list_unique,
            slpd.pay_money
        FROM
            sale_list sl
                INNER JOIN sale_list_pay_detail slpd ON sl.sale_list_unique = slpd.sale_list_unique
        where sl.shop_unique = #{shopUnique}
          AND sl.sale_list_state = 3
          and slpd.server_type != 0
        and sl.sale_list_datetime between #{startTime} and #{endTime}
    </select>
    <select id="selectBuyhooSaleListInfoCash" resultMap="selectBuyhooInfoDto">
        SELECT
            sl.sale_list_unique,
            sum(slpd.pay_money) as pay_money
        FROM
            sale_list sl
                INNER JOIN sale_list_pay_detail slpd ON sl.sale_list_unique = slpd.sale_list_unique
        where sl.shop_unique = #{shopUnique}
          AND sl.sale_list_state = 3
        and sl.sale_list_datetime between #{startTime} and #{endTime}
        group by sl.sale_list_unique
    </select>
    <select id="selectBuyhooSaleListTotal" resultMap="selectBuyhooInfoDto">
        SELECT
            sl.sale_list_unique,
            sum(slpd.pay_money) as pay_money
        FROM
            sale_list sl
                INNER JOIN sale_list_pay_detail slpd ON sl.sale_list_unique = slpd.sale_list_unique
        where sl.shop_unique = #{shopUnique}
          AND sl.sale_list_state = 3
          and slpd.server_type != 0
        and sl.sale_list_datetime between #{startTime} and #{endTime}
        group by sl.sale_list_unique
    </select>
    <resultMap id="selectBuyhooInfoDto" type="cc.buyhoo.tax.result.SelectBuyhooSaleListInfoDto">
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="pay_money" property="payMoney" />
    </resultMap>
</mapper>