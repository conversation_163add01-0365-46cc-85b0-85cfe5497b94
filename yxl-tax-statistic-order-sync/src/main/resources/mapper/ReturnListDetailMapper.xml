<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.ReturnListDetailMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.ReturnListDetailEntity" >
        <result column="ret_list_detail_id" property="retListDetailId" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="goods_barcode" property="goodsBarcode" />
        <result column="goods_name" property="goodsName" />
        <result column="ret_list_detail_count" property="retListDetailCount" />
        <result column="ret_list_detail_price" property="retListDetailPrice" />
        <result column="handle_way" property="handleWay" />
        <result column="ret_list_origin_price" property="retListOriginPrice" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="rsale_list_detail_id" property="rsaleListDetailId" />
    </resultMap>

    <sql id="Base_Column_List">
        ret_list_detail_id,
        sale_list_unique,
        goods_barcode,
        goods_name,
        ret_list_detail_count,
        ret_list_detail_price,
        handle_way,
        ret_list_origin_price,
        ret_list_unique,
        rsale_list_detail_id
    </sql>

</mapper>