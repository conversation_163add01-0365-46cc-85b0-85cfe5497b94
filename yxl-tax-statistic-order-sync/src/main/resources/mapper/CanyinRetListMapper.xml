<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinRetListMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinRetListEntity" >
        <result column="id" property="id" />
        <result column="shop_unique" property="shopUnique" />
        <result column="sale_list_main_unique" property="saleListMainUnique" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="ret_list_total" property="retListTotal" />
        <result column="ret_list_money" property="retListMoney" />
        <result column="retback_time" property="retbackTime" />
        <result column="ret_list_status" property="retListStatus" />
        <result column="ret_list_remarks" property="retListRemarks" />
        <result column="ret_list_reason" property="retListReason" />
        <result column="create_id" property="createId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        shop_unique,
        sale_list_main_unique,
        ret_list_unique,
        ret_list_total,
        ret_list_money,
        retback_time,
        ret_list_status,
        ret_list_remarks,
        ret_list_reason,
        create_id,
        create_by,
        create_time,
        modify_time
    </sql>
    <select id="selectCanyinReturnListInfo" resultMap="selectCanyinReturnListInfoDto">
        select crl.sale_list_main_unique as sale_list_unique,
        sum(crlp.pay_money) as pay_money
        from canyin_ret_list crl
        left join canyin_ret_list_pay_detail crlp on crl.ret_list_unique = crlp.ret_list_unique
        where crl.shop_unique = #{shopUnique}
        and crl.ret_list_status = 3
        and crlp.server_type != 1
        and crlp.server_type != 6
        and crl.retback_time >= #{startTime}
        and #{endTime} >= crl.retback_time
        group by crl.sale_list_main_unique
    </select>

    <resultMap id="selectCanyinReturnListInfoDto" type="cc.buyhoo.tax.result.SelectCanyinReturnListInfoDto">
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="pay_money" property="payMoney" />
    </resultMap>
</mapper>