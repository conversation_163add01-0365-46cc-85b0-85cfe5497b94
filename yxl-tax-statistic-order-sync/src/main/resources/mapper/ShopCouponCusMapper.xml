<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.ShopCouponCusMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.ShopCouponCusEntity" >
        <result column="shop_coupon_cus_id" property="shopCouponCusId" />
        <result column="shop_coupon_id" property="shopCouponId" />
        <result column="cus_unique" property="cusUnique" />
        <result column="use_status" property="useStatus" />
        <result column="create_time" property="createTime" />
        <result column="use_time" property="useTime" />
        <result column="coupon_code" property="couponCode" />
        <result column="use_shop_unique" property="useShopUnique" />
        <result column="order_no" property="orderNo" />
    </resultMap>

    <sql id="Base_Column_List">
        shop_coupon_cus_id,
        shop_coupon_id,
        cus_unique,
        use_status,
        create_time,
        use_time,
        coupon_code,
        use_shop_unique,
        order_no
    </sql>

</mapper>