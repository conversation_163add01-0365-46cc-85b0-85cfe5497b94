<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.ReturnListPaydetailMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.ReturnListPaydetailEntity" >
        <result column="id" property="id" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="pay_type" property="payType" />
        <result column="pay_money" property="payMoney" />
        <result column="service_type" property="serviceType" />
        <result column="mch_id" property="mchId" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sale_list_unique,
        ret_list_unique,
        pay_type,
        pay_money,
        service_type,
        mch_id
    </sql>

</mapper>