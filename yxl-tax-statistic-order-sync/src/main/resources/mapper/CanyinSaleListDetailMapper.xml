<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinSaleListDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinSaleListDetailEntity" >
        <result column="id" property="id" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="goods_barcode" property="goodsBarcode" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_sale_price" property="goodsSalePrice" />
        <result column="sale_list_detail_price" property="saleListDetailPrice" />
        <result column="pack_fee" property="packFee" />
        <result column="sale_list_detail_purprice" property="saleListDetailPurprice" />
        <result column="sale_list_detail_count" property="saleListDetailCount" />
        <result column="sale_list_detail_subtotal" property="saleListDetailSubtotal" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sale_list_unique,
        goods_barcode,
        goods_name,
        goods_id,
        goods_sale_price,
        sale_list_detail_price,
        pack_fee,
        sale_list_detail_purprice,
        sale_list_detail_count,
        sale_list_detail_subtotal,
        create_time,
        modify_time,
        del_flag
    </sql>
</mapper>