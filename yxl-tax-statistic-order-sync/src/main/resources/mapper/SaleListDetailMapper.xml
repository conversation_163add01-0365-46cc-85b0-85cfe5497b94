<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SaleListDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SaleListDetailEntity" >
        <result column="sale_list_detail_id" property="saleListDetailId" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="goods_barcode" property="goodsBarcode" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_picturepath" property="goodsPicturepath" />
        <result column="sale_list_detail_count" property="saleListDetailCount" />
        <result column="sale_list_detail_price" property="saleListDetailPrice" />
        <result column="sale_list_detail_subtotal" property="saleListDetailSubtotal" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_purprice" property="goodsPurprice" />
        <result column="commission_total" property="commissionTotal" />
        <result column="goods_old_price" property="goodsOldPrice" />
        <result column="goods_label" property="goodsLabel" />
        <result column="goods_beans_count" property="goodsBeansCount" />
        <result column="shop_beans_count" property="shopBeansCount" />
        <result column="sale_list_express_id" property="saleListExpressId" />
    </resultMap>

    <sql id="Base_Column_List">
        sale_list_detail_id,
        sale_list_unique,
        goods_barcode,
        goods_name,
        goods_picturepath,
        sale_list_detail_count,
        sale_list_detail_price,
        sale_list_detail_subtotal,
        goods_id,
        goods_purprice,
        commission_total,
        goods_old_price,
        goods_label,
        goods_beans_count,
        shop_beans_count,
        sale_list_express_id
    </sql>

    <!--根据detail_id查询-->
    <select id="queryDetailTotal" resultType="cc.buyhoo.tax.entity.SaleListDetailTotalEntity">
        SELECT
            sale_list_detail_id AS saleListDetailId,
            sale_list_detail_count AS saleListDetailCount,
            sale_list_detail_total AS saleListDetailTotal
        FROM
            sale_list_detail_total
        WHERE
            sale_list_detail_id IN
        <foreach collection="detailIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>