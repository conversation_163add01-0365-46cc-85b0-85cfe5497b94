<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.ReturnListMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.ReturnListEntity" >
        <result column="id" property="id" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="shop_unique" property="shopUnique" />
        <result column="ret_list_datetime" property="retListDatetime" />
        <result column="ret_list_total" property="retListTotal" />
        <result column="ret_list_count" property="retListCount" />
        <result column="ret_list_state" property="retListState" />
        <result column="ret_list_handlestate" property="retListHandlestate" />
        <result column="ret_list_remarks" property="retListRemarks" />
        <result column="staff_id" property="staffId" />
        <result column="mac_id" property="macId" />
        <result column="ret_origin" property="retOrigin" />
        <result column="ret_money_type" property="retMoneyType" />
        <result column="ret_list_total_money" property="retListTotalMoney" />
        <result column="ret_list_origin_total" property="retListOriginTotal" />
        <result column="out_refund_no" property="outRefundNo" />
        <result column="ret_back_datetime" property="retBackDatetime" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="ret_list_bean" property="retListBean" />
        <result column="ret_list_reason" property="retListReason" />
        <result column="ret_list_delfee" property="retListDelfee" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sale_list_unique,
        shop_unique,
        ret_list_datetime,
        ret_list_total,
        ret_list_count,
        ret_list_state,
        ret_list_handlestate,
        ret_list_remarks,
        staff_id,
        mac_id,
        ret_origin,
        ret_money_type,
        ret_list_total_money,
        ret_list_origin_total,
        out_refund_no,
        ret_back_datetime,
        ret_list_unique,
        ret_list_bean,
        ret_list_reason,
        ret_list_delfee
    </sql>

    <select id="selectBuyhooReturnListInfo" resultMap="selectBuyhooReturnListInfoDto">
        SELECT
            rl.sale_list_unique,
            sum(rlp.pay_money) as pay_money
        FROM
            return_list rl
                INNER JOIN return_list_paydetail rlp ON rl.ret_list_unique = rlp.ret_list_unique
        where rl.shop_unique = #{shopUnique}
          AND rl.ret_list_state = 2
          AND rl.ret_list_handlestate = 3
          AND rlp.service_type != 1
        and rl.ret_back_datetime between #{startTime} and #{endTime}
        group by rl.sale_list_unique
    </select>
    <select id="selectBuyhooReturnListInfoCash" resultMap="selectBuyhooReturnListInfoDto">
        SELECT
            rl.sale_list_unique,
            sum(rlp.pay_money) as pay_money
        FROM
            return_list rl
                INNER JOIN return_list_paydetail rlp ON rl.ret_list_unique = rlp.ret_list_unique
        where rl.shop_unique = #{shopUnique}
          AND rl.ret_list_state = 2
          AND rl.ret_list_handlestate = 3
        and rl.ret_back_datetime between #{startTime} and #{endTime}
        group by rl.sale_list_unique
    </select>
    <resultMap id="selectBuyhooReturnListInfoDto" type="cc.buyhoo.tax.result.SelectBuyhooReturnListInfoDto">
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="pay_money" property="payMoney" />
    </resultMap>

</mapper>