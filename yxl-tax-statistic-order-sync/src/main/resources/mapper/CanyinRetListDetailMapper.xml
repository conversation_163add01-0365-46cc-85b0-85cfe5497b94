<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinRetListDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinRetListDetailEntity" >
        <result column="id" property="id" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_barcode" property="goodsBarcode" />
        <result column="ret_count" property="retCount" />
        <result column="ret_price" property="retPrice" />
        <result column="rsale_list_detail_id" property="rsaleListDetailId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        ret_list_unique,
        goods_id,
        goods_barcode,
        ret_count,
        ret_price,
        rsale_list_detail_id,
        create_time,
        modify_time
    </sql>
</mapper>