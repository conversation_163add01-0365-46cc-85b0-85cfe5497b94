<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinSaleListMainPayDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinSaleListMainPayDetailEntity" >
        <result column="id" property="id" />
        <result column="sale_list_main_unique" property="saleListMainUnique" />
        <result column="pay_method" property="payMethod" />
        <result column="pay_money" property="payMoney" />
        <result column="server_type" property="serverType" />
        <result column="mch_id" property="mchId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="trans_index" property="transIndex" />
        <result column="sale_list_pay_unique" property="saleListPayUnique" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sale_list_main_unique,
        pay_method,
        pay_money,
        server_type,
        mch_id,
        create_time,
        modify_time,
        trans_index,
        sale_list_pay_unique
    </sql>
</mapper>