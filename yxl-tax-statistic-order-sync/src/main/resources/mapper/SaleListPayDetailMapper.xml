<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SaleListPayDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SaleListPayDetailEntity" >
        <result column="sale_list_pay_detail_id" property="saleListPayDetailId" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="pay_method" property="payMethod" />
        <result column="pay_money" property="payMoney" />
        <result column="server_type" property="serverType" />
        <result column="mch_id" property="mchId" />
    </resultMap>

    <sql id="Base_Column_List">
        sale_list_pay_detail_id,
        sale_list_unique,
        pay_method,
        pay_money,
        server_type,
        mch_id
    </sql>
</mapper>