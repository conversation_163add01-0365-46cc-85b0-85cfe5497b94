<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.ShopCouponMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.ShopCouponEntity" >
        <result column="shop_coupon_id" property="shopCouponId" />
        <result column="shop_unique" property="shopUnique" />
        <result column="designated_shop_unique" property="designatedShopUnique" />
        <result column="coupon_type" property="couponType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="meet_amount" property="meetAmount" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="type" property="type" />
        <result column="delete_status" property="deleteStatus" />
        <result column="give_status" property="giveStatus" />
        <result column="is_time" property="isTime" />
        <result column="is_daily" property="isDaily" />
        <result column="daily_num" property="dailyNum" />
        <result column="is_auto_grant" property="isAutoGrant" />
        <result column="grant_num" property="grantNum" />
        <result column="surplus_grant_num" property="surplusGrantNum" />
        <result column="exclusive_type" property="exclusiveType" />
        <result column="is_single_good" property="isSingleGood" />
        <result column="coupon_name" property="couponName" />
        <result column="coupon_img" property="couponImg" />
        <result column="use_shop_address" property="useShopAddress" />
        <result column="is_online" property="isOnline" />
        <result column="rule_description" property="ruleDescription" />
    </resultMap>

    <sql id="Base_Column_List">
        shop_coupon_id,
        shop_unique,
        designated_shop_unique,
        coupon_type,
        start_time,
        end_time,
        create_time,
        update_time,
        meet_amount,
        coupon_amount,
        type,
        delete_status,
        give_status,
        is_time,
        is_daily,
        daily_num,
        is_auto_grant,
        grant_num,
        surplus_grant_num,
        exclusive_type,
        is_single_good,
        coupon_name,
        coupon_img,
        use_shop_address,
        is_online,
        rule_description
    </sql>

</mapper>