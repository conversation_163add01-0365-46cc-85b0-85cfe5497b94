<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinRetListPayDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinRetListPayDetailEntity" >
        <result column="id" property="id" />
        <result column="ret_list_unique" property="retListUnique" />
        <result column="server_type" property="serverType" />
        <result column="pay_method" property="payMethod" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="pay_money" property="payMoney" />
        <result column="sale_list_pay_unique" property="saleListPayUnique" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        ret_list_unique,
        server_type,
        pay_method,
        create_time,
        modify_time,
        pay_money,
        sale_list_pay_unique
    </sql>
</mapper>