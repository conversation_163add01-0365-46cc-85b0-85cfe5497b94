<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.CanyinSaleListMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.CanyinSaleListEntity" >
        <result column="id" property="id" />
        <result column="shop_unique" property="shopUnique" />
        <result column="sale_list_main_unique" property="saleListMainUnique" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="sale_list_cashier" property="saleListCashier" />
        <result column="cus_unique" property="cusUnique" />
        <result column="table_id" property="tableId" />
        <result column="pickup_code" property="pickupCode" />
        <result column="sale_list_total" property="saleListTotal" />
        <result column="sale_list_actually_received" property="saleListActuallyReceived" />
        <result column="sale_list_ignore_money" property="saleListIgnoreMoney" />
        <result column="sale_list_discount_money" property="saleListDiscountMoney" />
        <result column="sale_type" property="saleType" />
        <result column="sale_list_handlestate" property="saleListHandlestate" />
        <result column="shipping_method" property="shippingMethod" />
        <result column="sale_list_state" property="saleListState" />
        <result column="sale_list_remarks" property="saleListRemarks" />
        <result column="sale_list_pur" property="saleListPur" />
        <result column="sale_list_payment" property="saleListPayment" />
        <result column="sale_list_total_count" property="saleListTotalCount" />
        <result column="receipt_datetime" property="receiptDatetime" />
        <result column="complete_datetime" property="completeDatetime" />
        <result column="pay_datetime" property="payDatetime" />
        <result column="trade_no" property="tradeNo" />
        <result column="serial_number" property="serialNumber" />
        <result column="sale_list_kindcount" property="saleListKindcount" />
        <result column="diners_count" property="dinersCount" />
        <result column="remark" property="remark" />
        <result column="pay_setting" property="paySetting" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="create_by" property="createBy" />
        <result column="del_flag" property="delFlag" />
        <result column="oper_lock" property="operLock" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        shop_unique,
        sale_list_main_unique,
        sale_list_unique,
        sale_list_cashier,
        cus_unique,
        table_id,
        pickup_code,
        sale_list_total,
        sale_list_actually_received,
        sale_list_ignore_money,
        sale_list_discount_money,
        sale_type,
        sale_list_handlestate,
        shipping_method,
        sale_list_state,
        sale_list_remarks,
        sale_list_pur,
        sale_list_payment,
        sale_list_total_count,
        receipt_datetime,
        complete_datetime,
        pay_datetime,
        trade_no,
        serial_number,
        sale_list_kindcount,
        diners_count,
        remark,
        pay_setting,
        create_id,
        create_time,
        modify_time,
        create_by,
        del_flag,
        oper_lock
    </sql>
</mapper>