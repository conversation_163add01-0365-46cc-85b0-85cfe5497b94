                   _         _______  _______  ______   _______  _______         _______           _        _______
|\     /||\     /|( \       (  ___  )(  ____ )(  __  \ (  ____ \(  ____ )       (  ____ \|\     /|( (    /|(  ____ \
( \   / )( \   / )| (       | (   ) || (    )|| (  \  )| (    \/| (    )|       | (    \/( \   / )|  \  ( || (    \/
 \ (_) /  \ (_) / | | _____ | |   | || (____)|| |   ) || (__    | (____)| _____ | (_____  \ (_) / |   \ | || |
  \   /    ) _ (  | |(_____)| |   | ||     __)| |   | ||  __)   |     __)(_____)(_____  )  \   /  | (\ \) || |
   ) (    / ( ) \ | |       | |   | || (\ (   | |   ) || (      | (\ (                ) |   ) (   | | \   || |
   | |   ( /   \ )| (____/\ | (___) || ) \ \__| (__/  )| (____/\| ) \ \__       /\____) |   | |   | )  \  || (____/\
   \_/   |/     \|(_______/ (_______)|/   \__/(______/ (_______/|/   \__/       \_______)   \_/   |/    )_)(_______/

Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
Spring Profiles Active: ${spring.profiles.active}
Spring Application Port: ${server.port}
Nacos Server-addr: ${spring.cloud.nacos.server-addr}
Nacos Username: ${spring.cloud.nacos.username}
Nacos Password: ${spring.cloud.nacos.password}
Nacos Group: ${spring.cloud.nacos.config.group}
Nacos Namespace: ${spring.cloud.nacos.config.namespace}

--------------------------------------------------------------------------------------------------------
