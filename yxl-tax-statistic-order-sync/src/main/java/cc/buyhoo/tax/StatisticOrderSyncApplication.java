package cc.buyhoo.tax;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@MapperScan("cc.buyhoo.tax.dao")
@EnableDiscoveryClient
@EnableDubbo
public class StatisticOrderSyncApplication {

    public static void main(String[] args) {
        SpringApplication.run(StatisticOrderSyncApplication.class,args);
    }

    static {
        //同一台服务器部署多个环境，dubbo缓存文件重复问题
        System.setProperty("user.home", "dubboCache");
    }

}
