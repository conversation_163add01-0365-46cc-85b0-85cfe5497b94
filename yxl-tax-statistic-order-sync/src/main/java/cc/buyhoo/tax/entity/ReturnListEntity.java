package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "return_list")
public class ReturnListEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退货单号
     */
    @TableId
    private Integer id;

    /**
    * 销售订单编号
    */
    private String saleListUnique;

    /**
    * 退款的店铺编号
    */
    private String shopUnique;

    /**
    * 退货日期
    */
    private Date retListDatetime;

    /**
    * 退货总金额
    */
    private BigDecimal retListTotal;

    /**
    * 退货总数量
    */
    private BigDecimal retListCount;

    /**
    * 退款状态:1-未退款，2-已退款
    */
    private String retListState;

    /**
    * 退货申请受理状态：1未处理，2-已受理，3受理完毕，4、驳回
    */
    private String retListHandlestate;

    /**
    * 备注信息
    */
    private String retListRemarks;

    /**
    * 操作员工编号
    */
    private Integer staffId;

    /**
    * 退货机器macid
    */
    private String macId;

    /**
    * 退货来源：1、pc收银；2、网页；3、小程序；4、app
    */
    private Integer retOrigin;

    /**
    * 退款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡 6 易通（原路退回）;7、赊账（原订单赊账）
    */
    private Integer retMoneyType;

    /**
    * 实际退款金额
    */
    private BigDecimal retListTotalMoney;

    /**
    * 退款商品销售时总原价
    */
    private BigDecimal retListOriginTotal;

    /**
    * 退款单号
    */
    private String outRefundNo;

    /**
    * 退款到帐时间
    */
    private Date retBackDatetime;

    /**
    * 退款申请单号
    */
    private String retListUnique;

    /**
    * 扣除的百货豆数量（因消费而赠送，需要扣回的）
    */
    private Integer retListBean;

    /**
    * 申请退款原因
    */
    private String retListReason;

    /**
    * 退还的配送费
    */
    private BigDecimal retListDelfee;
}