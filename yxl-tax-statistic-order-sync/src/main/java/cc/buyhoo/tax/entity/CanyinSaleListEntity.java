package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_sale_list")
public class CanyinSaleListEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 主订单类型
    */
    private String saleListMainUnique;

    /**
    * 订单编号
    */
    private String saleListUnique;

    /**
    * 员工id
    */
    private Long saleListCashier;

    /**
    * 会员编号
    */
    private Long cusUnique;

    /**
    * 订单对应桌号，打包订单可没有桌号
    */
    private Long tableId;

    /**
    * 取餐码
    */
    private String pickupCode;

    /**
    * 订单应付总金额
    */
    private BigDecimal saleListTotal;

    /**
    * 订单实付金额（包含储值卡金额），为商户收到的实际金额，不包含优惠券，包含余额
    */
    private BigDecimal saleListActuallyReceived;

    /**
    * 抹零金额
    */
    private BigDecimal saleListIgnoreMoney;

    /**
    * 优惠金额
    */
    private BigDecimal saleListDiscountMoney;

    /**
    * 订单类型：1、堂食订单；2、打包订单；3、外卖订单；详细见sys_dict_data表:canyin_sale_type
    */
    private String saleType;

    /**
    * 订单状态，见sys_dict_data表:canyin_sale_list_handlestate
    */
    private String saleListHandlestate;

    /**
    * 配送方式：1、送货上门；2、自提；
    */
    private int shippingMethod;

    /**
    * 订单付款状态；1、未付款；2、已付款；3、支付中
    */
    private int saleListState;

    /**
    * 订单备注
    */
    private String saleListRemarks;

    /**
    * 商品总进价
    */
    private BigDecimal saleListPur;

    /**
    * 支付方式，参见sys_dict_data中的sys_sale_payment
    */
    private String saleListPayment;

    /**
    * 商品累计数量
    */
    private BigDecimal saleListTotalCount;

    /**
    * 订单收货时间
    */
    private Date receiptDatetime;

    /**
    * 订单完成时间（如果没有意外，收货时间就是完成时间）
    */
    private Date completeDatetime;

    /**
    * 支付完成时间，统计订单金额时，以此时间为标准
    */
    private Date payDatetime;

    /**
    * 交易流水号，使用第三方支付时存储，用于查询交易信息
    */
    private String tradeNo;

    /**
    * 流水号（同一个桌子，流水号相同的订单为同一桌客户）
    */
    private Long serialNumber;

    /**
    * 商品类别数量
    */
    private Integer saleListKindcount;

    /**
    * 就餐人数
    */
    private Integer dinersCount;

    /**
    * 下单备注
    */
    private String remark;

    /**
    * 1、先吃后付；2、先付后吃
    */
    private Integer paySetting;

    /**
    * 下单人id
    */
    private Long createId;

    /**
    * 订单创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;

    /**
    * 下单人名称
    */
    private String createBy;

    /**
    * 删除标识，0-正常，1-删除
    */
    private int delFlag;

    /**
    * 是否锁定订单：0、未锁定；1、已锁定（禁止任何更新、添加相关操作，支付回调除外）
    */
    private Integer operLock;

}