package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_ret_list_detail")
public class CanyinRetListDetailEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 退款单号
    */
    private String retListUnique;

    /**
    * 商品id
    */
    private Long goodsId;

    /**
    * 商品条码
    */
    private String goodsBarcode;

    /**
    * 退款商品数量
    */
    private BigDecimal retCount;

    /**
    * 退款商品的金额
    */
    private BigDecimal retPrice;

    /**
    * 该商品在订单详情中的id，由于商品规格一样，此字段必须上传，可通过此字段关联商品的具体规格信息
    */
    private Long rsaleListDetailId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;
}