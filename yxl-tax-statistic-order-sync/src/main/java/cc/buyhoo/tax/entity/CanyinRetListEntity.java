package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_ret_list")
public class CanyinRetListEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 主订单编号
    */
    private String saleListMainUnique;

    /**
    * 退款单号
    */
    private String retListUnique;

    /**
    * 退款应退金额
    */
    private BigDecimal retListTotal;

    /**
    * 实际退款金额
    */
    private BigDecimal retListMoney;

    /**
    * 退款到帐时间，统计时，需以此字段作为实际退款时间，由三方机构退款时，取三方机构实际退款时间，防止因零点导致的对账差
    */
    private Date retbackTime;

    /**
    * 退款订单状态：见sys_dict_data表：canyin_ret_list_status
    */
    private String retListStatus;

    /**
    * 申请退款备注信息
    */
    private String retListRemarks;

    /**
    * 用户主动申请退款，需要填写退款原因
    */
    private String retListReason;

    /**
    * 创建人id
    */
    private Long createId;

    /**
    * 创建人
    */
    private String createBy;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;
}