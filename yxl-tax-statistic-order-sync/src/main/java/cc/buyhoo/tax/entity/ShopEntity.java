package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 店铺 表
 * @ClassName ShopEntity
 * <AUTHOR>
 * @Date 2023/9/4 16:16
 **/
@Data
@TableName(value = "shops")
public class ShopEntity implements Serializable {
    private String shopName;
    private String shopUnique;
    private String shopAddressDetail;
    private Integer shopType;
    private String shopPhone;
}
