package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName(value = "return_list_detail")
public class ReturnListDetailEntity implements Serializable {

    /**
    * id
    */
    @TableId
    private Integer retListDetailId;

    /**
    * 退货单唯一标识符
    */
    private String saleListUnique;

    /**
    * 商品条形码
    */
    private String goodsBarcode;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 退货数量
    */
    private BigDecimal retListDetailCount;

    /**
    * 退货价格
    */
    private BigDecimal retListDetailPrice;

    /**
    * 退回货物处理方式：1、入库；2、报损（过期产品）；3、其他
    */
    private Integer handleWay;

    /**
    * 退货商品的销售原价
    */
    private BigDecimal retListOriginPrice;

    /**
    * 退款单号
    */
    private String retListUnique;

    /**
    * 订单详情id
    */
    private Integer rsaleListDetailId;

}