package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_ret_list_pay_detail")
public class CanyinRetListPayDetailEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 退款单号
    */
    private String retListUnique;

    /**
    * 见sys_dict_data 表：canyin_server_type
    */
    private String serverType;

    /**
    * 见sys_dict_data表：canyin_pay_method
    */
    private String payMethod;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;

    /**
    * 退款金额
    */
    private BigDecimal payMoney;

    /**
    * 支付订单编号（非主订单编号）
    */
    private String saleListPayUnique;
}