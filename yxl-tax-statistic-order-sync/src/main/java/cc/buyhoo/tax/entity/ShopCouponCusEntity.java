package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * shop_coupon_cus
 */
@Data
@TableName(value = "shop_coupon_cus")
public class ShopCouponCusEntity {


    /**
    * shop_coupon_cus_id
    */
    @TableId
    private Integer shopCouponCusId;

    /**
    * 优惠券id
    */
    private Integer shopCouponId;

    /**
    * 用户编号
    */
    private Long cusUnique;

    /**
    * 0:未用 1:已使用
    */
    private Integer useStatus;

    /**
    * 领取时间
    */
    private Date createTime;

    /**
    * 使用时间
    */
    private Date useTime;

    /**
    * 兑换码
    */
    private String couponCode;

    /**
    * 使用的店铺编号
    */
    private String useShopUnique;

    /**
    * 第三方单号，充值单号或者订单单号
    */
    private String orderNo;

}