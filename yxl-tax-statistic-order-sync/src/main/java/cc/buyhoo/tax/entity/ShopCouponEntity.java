package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券信息配置表
 */
@Data
@TableName(value = "shop_coupon")
public class ShopCouponEntity implements Serializable {

    /**
    * shop_coupon_id
    */
    @TableId
    private Integer shopCouponId;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 指定的店铺店铺编号或者连锁店连锁编号，平台通用券用-1表示;平台填-1，连锁总店和普通店默认自己，子店铺默认不可以添加优惠券
    */
    private Long designatedShopUnique;

    /**
    * 优惠券类型：1、平台通用券；2、连锁店通用；3、指定店铺可用；
    */
    private Integer couponType;

    /**
    * 开始时间
    */
    private Date startTime;

    /**
    * 结束时间
    */
    private Date endTime;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 满足金额
    */
    private BigDecimal meetAmount;

    /**
    * 优惠金额
    */
    private BigDecimal couponAmount;

    /**
    * 1：全品类 2：仅限非折扣商品
    */
    private Integer type;

    /**
    * 0:未删除 1:已删除
    */
    private Integer deleteStatus;

    /**
    * 是否是充值赠送优惠券 0:否 1:是
    */
    private Integer giveStatus;

    /**
    * 是否分时段优惠券：1否 2是
    */
    private Integer isTime;

    /**
    * 分时段优惠券是否可每天使用：1否 2是
    */
    private Integer isDaily;

    /**
    * 分时段优惠券每天使用次数
    */
    private Integer dailyNum;

    /**
    * 是否自动发放：1否 2是
    */
    private Integer isAutoGrant;

    /**
    * 发放数量：-1为不限
    */
    private Integer grantNum;

    /**
    * 剩余发放数量
    */
    private Integer surplusGrantNum;

    /**
    * 专享优惠券类型：0、非专享 ；1、联通专享；2、加油专享
    */
    private Integer exclusiveType;

    /**
    * 是否单品 1:是 2:否
    */
    private Integer isSingleGood;

    /**
    * 优惠券名称
    */
    private String couponName;

    /**
    * 优惠券图片
    */
    private String couponImg;

    /**
    * 店铺使用地址
    */
    private String useShopAddress;

    /**
    * 1：线上会员 2:线下会员
    */
    private Integer isOnline;

    /**
    * 规则说明
    */
    private String ruleDescription;
}