package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName(value = "return_list_paydetail")
public class ReturnListPaydetailEntity implements Serializable {
    /**
    * id
    */
    @TableId
    private Integer id;

    /**
    * 订单单号
    */
    private String saleListUnique;

    /**
    * 退货单号，一个订单可退多次，也可以有几种不同的退款方式
    */
    private String retListUnique;

    /**
    * 退款的收款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡；6、其他；7、优惠券；8、百货豆rn查询时，先判断service_type，在判断pay_type
    */
    private Integer payType;

    /**
    * 退款金额
    */
    private BigDecimal payMoney;

    /**
    * 现金支付服务端：1、线下操作；2、拉卡拉平台； 3 易通 4、微信平台；5、其他平台 ，6、合利宝，7、云平台
    */
    private Integer serviceType;

    /**
    * 退款的账号
    */
    private String mchId;
}