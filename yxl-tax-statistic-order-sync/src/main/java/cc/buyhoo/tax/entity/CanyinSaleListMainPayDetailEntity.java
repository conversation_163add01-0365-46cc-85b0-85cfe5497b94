package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_sale_list_main_pay_detail")
public class CanyinSaleListMainPayDetailEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 订单编号
    */
    private String saleListMainUnique;

    /**
    * 付款方式：见餐饮sys_dict_data表、canyin_pay_method
    */
    private Integer payMethod;

    /**
    * pay_money
    */
    private BigDecimal payMoney;

    /**
    * 收款方式：见sys_dict_data表、canyin_server_type
    */
    private Integer serverType;

    /**
    * 收款账户
    */
    private String mchId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;

    /**
    * 三方支付流水号
    */
    private String transIndex;

    /**
    * 支付订单号
    */
    private String saleListPayUnique;
}