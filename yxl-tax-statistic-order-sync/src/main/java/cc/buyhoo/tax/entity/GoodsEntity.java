package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 百货端商品实体
 * @ClassName GoodsEntity
 * <AUTHOR>
 * @Date 2023/7/29 14:20
 **/
@Data
@TableName(value = "goods")
public class GoodsEntity implements Serializable {
    private static final long serialVersionUID = -3819434804845604983L;

    /**
     * 商品编号
     */
    @TableId
    private Integer goodsId;

    /**
     * 店铺唯一标识
     */
    private Long shopUnique;

    /**
     * 店铺唯一标识符
     */
    private String goodsBarcode;

    /**
     * 商品品牌
     */
    private String goodsBrand;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品别名
     */
    private String goodsAlias;

    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;

    /**
     * 商品售价
     */
    private BigDecimal goodsSalePrice;

    /**
     * 商品规格
     */
    private String goodsStandard;

    /**
     * 商品计价单位
     */
    private String goodsUnit;

    /**
     * 商品线上售价
     */
    private BigDecimal goodsWebSalePrice;

}
