package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "canyin_sale_list_detail")
public class CanyinSaleListDetailEntity {
    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 订单编号
    */
    private String saleListUnique;

    /**
    * 商品条码（餐饮行业似乎不需要）
    */
    private String goodsBarcode;

    /**
    * 商品名称，包含商品选择的规格，用“-”隔开
    */
    private String goodsName;

    /**
    * 商品id，防止商品删除后，无法统计商品的问题
    */
    private Long goodsId;

    /**
    * 商品销售原价
    */
    private BigDecimal goodsSalePrice;

    /**
    * 商品售价（含规格、配菜、打包费）
    */
    private BigDecimal saleListDetailPrice;

    /**
    * 打包费
    */
    private BigDecimal packFee;

    /**
    * 商品进价
    */
    private BigDecimal saleListDetailPurprice;

    /**
    * 商品数量
    */
    private BigDecimal saleListDetailCount;

    /**
    * 小计，钱统一到分
    */
    private BigDecimal saleListDetailSubtotal;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date modifyTime;

    /**
    * 删除标识，0-正常，1-删除
    */
    private int delFlag;
}