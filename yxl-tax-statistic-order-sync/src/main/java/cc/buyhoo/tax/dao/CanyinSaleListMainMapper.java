package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.CanyinSaleListMainEntity;
import cc.buyhoo.tax.result.SelectCanyinSaleListInfoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

public interface CanyinSaleListMainMapper extends BaseMapper<CanyinSaleListMainEntity> {

    /**
     * 根据条件查询
     * @param map
     * @return
     */
    public List<CanyinSaleListMainEntity> querySaleListMainPage(Map<String,Object> map);

    List<SelectCanyinSaleListInfoDto> selectCanyinSaleListInfoCash(Map<String,Object> map);

    List<SelectCanyinSaleListInfoDto> selectCanyinSaleListInfo(Map<String,Object> map);
}
