package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.SaleListDetailEntity;
import cc.buyhoo.tax.entity.SaleListDetailTotalEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleListDetailMapper extends BaseMapper<SaleListDetailEntity> {

    /**
     * 根据detail_id查询
     * @param detailIdList
     * @return
     */
    public List<SaleListDetailTotalEntity> queryDetailTotal(@Param("detailIdList") List<Integer> detailIdList);

}
