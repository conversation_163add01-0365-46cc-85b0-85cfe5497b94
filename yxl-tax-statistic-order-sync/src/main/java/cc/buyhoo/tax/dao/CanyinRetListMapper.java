package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.CanyinRetListEntity;
import cc.buyhoo.tax.result.SelectCanyinReturnListInfoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CanyinRetListMapper extends BaseMapper<CanyinRetListEntity> {

    List<SelectCanyinReturnListInfoDto> selectCanyinReturnListInfo(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
