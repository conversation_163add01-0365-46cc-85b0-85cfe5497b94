package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.ReturnListEntity;
import cc.buyhoo.tax.result.SelectBuyhooReturnListInfoDto;
import cc.buyhoo.tax.result.SelectBuyhooSaleListInfoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ReturnListMapper extends BaseMapper<ReturnListEntity> {

    List<SelectBuyhooReturnListInfoDto> selectBuyhooReturnListInfoCash(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
    List<SelectBuyhooReturnListInfoDto> selectBuyhooReturnListInfo(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
