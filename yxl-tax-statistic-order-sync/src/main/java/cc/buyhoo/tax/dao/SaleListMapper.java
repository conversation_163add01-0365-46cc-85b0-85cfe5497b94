package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.SaleListEntity;
import cc.buyhoo.tax.params.QueryOrderCountParams;
import cc.buyhoo.tax.result.SelectBuyhooSaleListInfoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SaleListMapper extends BaseMapper<SaleListEntity> {

    /**
     * 支付订单统计
     * @param params
     * @return
     */
    public long queryOrderCount(QueryOrderCountParams params);

    /**
     * 根据条件查询
     * @return
     */
    public List<SaleListEntity> querySaleListPage(Map<String,Object> map);

    List<SelectBuyhooSaleListInfoDto> selectBuyhooSaleListInfo(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SelectBuyhooSaleListInfoDto> selectBuyhooSaleListInfoCash(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
