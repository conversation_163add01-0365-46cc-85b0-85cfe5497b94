package cc.buyhoo.tax.task;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.facade.*;
import cc.buyhoo.tax.facade.enums.BusSyncTypeEnum;
import cc.buyhoo.tax.facade.params.busShop.QueryBindShopParams;
import cc.buyhoo.tax.facade.params.returnList.ReturnListPaydetailRemote;
import cc.buyhoo.tax.facade.params.returnList.ReturnListRemote;
import cc.buyhoo.tax.facade.params.returnList.SyncReturnList;
import cc.buyhoo.tax.facade.params.returnList.SyncReturnListParams;
import cc.buyhoo.tax.facade.params.saleList.*;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cc.buyhoo.tax.facade.result.busSyncRecord.BusSyncRecordDto;
import cc.buyhoo.tax.facade.result.busSyncRecord.QueryBusSyncRecordResult;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class CanyinOrderSyncCustomTask extends AbstractSyncTaskHelper {

    @DubboReference
    private SysCompanyFacade sysCompanyFacade;

    @DubboReference
    private BusShopFacade busShopFacade;

    @DubboReference(retries = 0)
    private SaleListFacade saleListFacade;

    @DubboReference(retries = 0)
    private ReturnListFacade returnListFacade;

    @Resource
    private CanyinSaleListMainMapper canyinSaleListMainMapper;

    @Resource
    private CanyinSaleListMapper canyinSaleListMapper;

    @Resource
    private CanyinSaleListDetailMapper canyinSaleListDetailMapper;

    @Resource
    private CanyinSaleListMainPayDetailMapper canyinSaleListMainPayDetailMapper;

    @Resource
    private CanyinRetListMapper canyinRetListMapper;

    @Resource
    private CanyinRetListPayDetailMapper canyinRetListPayDetailMapper;

    @DubboReference
    private BusSyncRecordFacade busSyncRecordFacade;

    @Value("${order.sync.task.pageSize}")
    private Integer orderSyncTaskPageSize;


    //key:餐饮pay_menthod,value:纳统payMethod
    final static Map<String, Integer> payMethodMap = new HashMap<>();
    static {
        payMethodMap.put("1", 1);
        payMethodMap.put("2", 2);
        payMethodMap.put("3", 3);
        payMethodMap.put("4", 4);
        payMethodMap.put("5", 5);
        payMethodMap.put("6", 10);
        payMethodMap.put("7", 37);
        payMethodMap.put("8", 38);
        payMethodMap.put("9", 13);
    };


    //key:餐饮server_type,value:纳统server_type
    final static Map<String, Integer> serverTypeMap = new HashMap<>();
    static {
        serverTypeMap.put("1", 0);
        serverTypeMap.put("2", 6);
        serverTypeMap.put("3", 8);
        serverTypeMap.put("4", 7);
        serverTypeMap.put("5", 11);
        serverTypeMap.put("6", 0);
    };

    // 餐饮支付方式(31现金，32支付宝，33微信，34银行卡，35储值卡，36美团，37饿了么，38混合支付，39金圈支付，40积分兑换，41百货豆，42金圈支付，43金圈支付，44金圈聚合码)
    final static Map<String, Integer> saleListPaymentMap = new HashMap<String, Integer>();
    static {
        saleListPaymentMap.put("1", 1);
        saleListPaymentMap.put("2", 2);
        saleListPaymentMap.put("3", 3);
        saleListPaymentMap.put("4", 4);
        saleListPaymentMap.put("5", 5);
        saleListPaymentMap.put("6", 6);
        saleListPaymentMap.put("7", 7);
        saleListPaymentMap.put("8", 8);
        saleListPaymentMap.put("9", 9);
        saleListPaymentMap.put("10", 10);
        saleListPaymentMap.put("11", 11);
        saleListPaymentMap.put("12", 13);
        saleListPaymentMap.put("13", 13);
        saleListPaymentMap.put("14", 14);
    };

    @XxlJob("canyinOrderSyncJobHandler")
    public void canyinOrderSyncJobHandler() {
//        String jobParam = XxlJobHelper.getJobParam();

        //查询分公司
        Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
        if (branchCompResult.hasFail()) {
            XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
            return;
        }
        List<QueryBranchCompanyDto> companyList = branchCompResult.getData().getCompanyList();

        //按纳统企业统计
        for (QueryBranchCompanyDto comp : companyList) {
            //查询所有的店铺
            Result<QueryBindShopResult> shopResult = queryBindShops(comp.getId());
            if (shopResult.hasFail()) {
                XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
                continue;
            }
            Date endTime = DateUtil.date();
            Date startTime = DateUtil.beginOfDay(endTime);
            // 查询上一次同步记录
            Result<QueryBusSyncRecordResult> syncResult = busSyncRecordFacade.queryLastOne(comp.getId(), BusSyncTypeEnum.FOOD_ORDER.getCode());
            QueryBusSyncRecordResult recordResult = syncResult.getData();
            if (ObjectUtil.isNotNull(recordResult) && ObjectUtil.isNotNull(recordResult.getEndTime())) {
                startTime = recordResult.getEndTime();
            }
            BusSyncRecordDto recordDto = new BusSyncRecordDto();
            recordDto.setCompanyId(comp.getId());
            recordDto.setStartTime(startTime);
            recordDto.setEndTime(endTime);
            recordDto.setSyncType(BusSyncTypeEnum.FOOD_ORDER.getCode());
            Result<BusSyncRecordDto> recordDtoResult = busSyncRecordFacade.insertBusSyncRecord(recordDto);
            if (ObjectUtil.isNotNull(recordDtoResult.getData())) {
                recordDto = recordDtoResult.getData();
            }
            //退单列表
            List<SyncReturnList> companyReturnList = new ArrayList<>();
            //餐饮店铺列表
            List<QueryBindShopDto> shopList = shopResult.getData().getShopList();
            //采购单总金额
            BigDecimal inventoryTotalCashMoney = BigDecimal.ZERO;
            BigDecimal inventoryTotalOnlineMoney = BigDecimal.ZERO;
            Long saleListCount = 0L;
            for (QueryBindShopDto shop : shopList) {
                try {
                    //在线支付金额
                    BigDecimal onlinePay = BigDecimal.ZERO;
                    //现金支付金额
                    BigDecimal cashPay = BigDecimal.ZERO;
                    //订单开始id
                    long startSaleListMainId = 0;
                    while (true) {
                        //查询订单数据
                        List<CanyinSaleListMainEntity> saleListMainList = selectQueryCanyinSaleListMain(shop.getShopUnique(), startTime, endTime, startSaleListMainId);
                        //结束循环
                        if (ObjectUtil.isEmpty(saleListMainList)) break;
                        //继续进行下次分页查询
                        startSaleListMainId = saleListMainList.get(saleListMainList.size() - 1).getId();

                        List<String> mainUniqueList = saleListMainList.stream().map(CanyinSaleListMainEntity::getSaleListMainUnique).collect(Collectors.toList());

                        //查询canyin_sale_list
                        List<CanyinSaleListEntity> saleList = selectCanyinSaleList(mainUniqueList);

                        List<String> saleListUniqueList = saleList.stream().map(CanyinSaleListEntity::getSaleListUnique).collect(Collectors.toList());
                        //查询canyin_sale_list_detail
                        List<CanyinSaleListDetailEntity> detailList = selectCanyinSaleListDetail(saleListUniqueList);

                        //查询支付信息
                        List<CanyinSaleListMainPayDetailEntity> mainPayDetailList = selectMainPayDetail(mainUniqueList);

                        //订单同步
                        List<SaveSaleList> saveList = syncSaleList(shop, saleListMainList, saleList, detailList, mainPayDetailList);

                        //计算线上支付
                        List<SaleListPayDetail> payList = saveList.stream().map(SaveSaleList::getSaleListPayDetailList).flatMap(p -> p.stream()).collect(Collectors.toList());
                        BigDecimal olMoney = payList.stream().filter(p -> p.getServerType() != 0).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal cashMoney = payList.stream().filter(p -> p.getServerType() == 0).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                        onlinePay = NumberUtil.add(onlinePay, olMoney);
                        cashPay = NumberUtil.add(cashPay, cashMoney);

                        saleListCount += saleList.size();
                    }

                    convertReturnList(shop, startTime, endTime, companyReturnList);
                    //增加企业统计金额
                    inventoryTotalCashMoney = NumberUtil.add(inventoryTotalCashMoney, cashPay);
                    inventoryTotalOnlineMoney = NumberUtil.add(inventoryTotalOnlineMoney, onlinePay);
                } catch (Exception e) {
                    e.printStackTrace();
                    XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
                }
            }

            //根据公司保存退款单
            syncCompanyReturnList(comp.getId(), companyReturnList);
            //保存采购信息，餐饮的只需要保存inventory_order即可，只做首页统计使用
            saveInventoryOrder(comp.getId(), 2, inventoryTotalCashMoney, inventoryTotalOnlineMoney);

            recordDto.setSaleListCount(saleListCount);
            busSyncRecordFacade.updateBusSyncRecord(recordDto);
        }
    }

    /**
     * 查询公司绑定的店铺
     *
     * @param companyId
     * @return
     */
    private Result<QueryBindShopResult> queryBindShops(Long companyId) {
        //查询所有的店铺
        QueryBindShopParams shopParams = new QueryBindShopParams();
        shopParams.setCompanyId(companyId);
        shopParams.setSyncCanyinData(Integer.valueOf(1));
        return busShopFacade.queryBindShopByCompany(shopParams);
    }

    private Map<String, String> handlePredayDate() {
        Map<String, String> map = new HashMap<>();
        Date curr = new Date();
        DateTime dateTime = DateUtil.offsetDay(curr, -1);
        String preDayStr = DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN);
        String dateStart = StringUtils.join(preDayStr, " 00:00:00");
        String dateEnd = StringUtils.join(preDayStr, " 23:59:59");
        map.put("dateStart", dateStart);
        map.put("dateEnd", dateEnd);

        return map;
    }

    /**
     * 查询主订单
     *
     * @param shopUnique
     * @param startSaleListMainId
     * @return
     */
    private List<CanyinSaleListMainEntity> selectQueryCanyinSaleListMain(Long shopUnique, Date startTime, Date endTime, Long startSaleListMainId) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("dateStart", startTime);
        queryMap.put("dateEnd", endTime);
        queryMap.put("shopUnique", shopUnique);
        queryMap.put("mainId", startSaleListMainId);
        queryMap.put("pageSize", orderSyncTaskPageSize);

        return canyinSaleListMainMapper.querySaleListMainPage(queryMap);
    }

    /**
     * 查询子订单
     *
     * @param mainUniqueList
     * @return
     */
    private List<CanyinSaleListEntity> selectCanyinSaleList(List<String> mainUniqueList) {
        LambdaQueryWrapper<CanyinSaleListEntity> saleListWrapper = new LambdaQueryWrapper<>();
        saleListWrapper.in(CanyinSaleListEntity::getSaleListMainUnique, mainUniqueList);
        return canyinSaleListMapper.selectList(saleListWrapper);
    }

    /**
     * 查询订单详情
     *
     * @param saleListUniqueList
     * @return
     */
    private List<CanyinSaleListDetailEntity> selectCanyinSaleListDetail(List<String> saleListUniqueList) {
        LambdaQueryWrapper<CanyinSaleListDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(CanyinSaleListDetailEntity::getSaleListUnique, saleListUniqueList);
        return canyinSaleListDetailMapper.selectList(detailWrapper);
    }

    /**
     * 查询支付信息
     *
     * @param mainUniqueList
     * @return
     */
    private List<CanyinSaleListMainPayDetailEntity> selectMainPayDetail(List<String> mainUniqueList) {
        LambdaQueryWrapper<CanyinSaleListMainPayDetailEntity> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.in(CanyinSaleListMainPayDetailEntity::getSaleListMainUnique, mainUniqueList);
        return canyinSaleListMainPayDetailMapper.selectList(mainWrapper);
    }

    /**
     * 订单同步
     *
     * @return
     */
    private List<SaveSaleList> syncSaleList(QueryBindShopDto shop, List<CanyinSaleListMainEntity> saleListMainList, List<CanyinSaleListEntity> saleList, List<CanyinSaleListDetailEntity> detailList, List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        List<SaveSaleList> saveList = new ArrayList<>();
        for (CanyinSaleListMainEntity main : saleListMainList) {
            List<CanyinSaleListMainPayDetailEntity> dList = mainPayDetailList.stream().filter(d -> main.getSaleListMainUnique().equals(d.getSaleListMainUnique())).collect(Collectors.toList());
            saveList.add(convertSaveList(shop, main, saleList, detailList, dList));
        }

        //参数构建
        SaveSaleListParams params = new SaveSaleListParams();
        params.setSaveList(saveList);
        saleListFacade.saveSaleList(params);

        return saveList;
    }

    /**
     * 订单数据转换
     */
    private SaveSaleList convertSaveList(QueryBindShopDto shop, CanyinSaleListMainEntity main, List<CanyinSaleListEntity> saleList, List<CanyinSaleListDetailEntity> detailList, List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        SaveSaleList saveSaleList = new SaveSaleList();

        List<SaleListPayDetail> payDetailList = convertTaxSaleListPayDetailList(main, mainPayDetailList);
        saveSaleList.setSaleListPayDetailList(payDetailList);
        saveSaleList.setSaleListDetailList(convertTaxSaleListDetail(main, saleList, detailList));
        saveSaleList.setSaleList(convertTaxSaleList(shop, main, payDetailList));

        return saveSaleList;
    }

    /**
     * 餐饮订单信息转换
     *
     * @param shop
     * @param main
     * @return
     */
    private SaleList convertTaxSaleList(QueryBindShopDto shop, CanyinSaleListMainEntity main, List<SaleListPayDetail> payDetailList) {
        //key：餐饮sale_list_state,value：纳统sale_list_state
        Map<Integer, Integer> saleListStateMap = new HashMap<>();
        saleListStateMap.put(1, 2);
        saleListStateMap.put(2, 3);

        //线上支付金额
        Integer[] onlineServerTypeList = { 6, 7, 8, 11 };
        BigDecimal onlinePay = payDetailList.stream().filter(p -> ArrayUtil.contains(onlineServerTypeList, p.getServerType())).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

        //纳统sale_list
        SaleList sl = new SaleList();
        sl.setCompanyId(shop.getCompanyId());
        sl.setShopUnique(shop.getShopUnique());
        sl.setShopName(shop.getShopName());
        sl.setSaleType(0);
        sl.setSaleListUnique(main.getSaleListMainUnique());
        sl.setSaleListName("");
        sl.setSaleListTotal(main.getSaleListTotal());
        sl.setSaleListActuallyReceived(main.getSaleListActuallyReceived());
        BigDecimal serviceFeeRate = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
            serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(100));
        }
        BigDecimal serviceFee = NumberUtil.mul(new BigDecimal(String.valueOf(onlinePay)), serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        sl.setSaleListServiceFee(serviceFee);
        sl.setTradeNo(main.getTradeNo());
        sl.setPayTime(main.getPayDatetime());
        sl.setSaleListDatetime(main.getCreateTime());
        sl.setSaleListState(saleListStateMap.get(main.getSaleListState()));
        sl.setSaleListPayment(saleListPaymentMap.get(main.getSaleListPayment()));
        sl.setCouponAmount(BigDecimal.ZERO);
        sl.setShopCouponId(0);

        return sl;
    }

    /**
     * 餐饮订单详情转换
     *
     * @return
     */
    private List<SaleListDetail> convertTaxSaleListDetail(CanyinSaleListMainEntity main, List<CanyinSaleListEntity> saleList, List<CanyinSaleListDetailEntity> detailList) {
        List<SaleListDetail> taxDetailList = new ArrayList<>();

        //筛选子订单
        List<CanyinSaleListEntity> sList = saleList.stream().filter(s -> main.getSaleListMainUnique().equals(s.getSaleListMainUnique())).collect(Collectors.toList());
        List<String> saleListUniqueList = sList.stream().map(CanyinSaleListEntity::getSaleListUnique).collect(Collectors.toList());
        //筛选订单详情
        List<CanyinSaleListDetailEntity> dList = detailList.stream().filter(d -> saleListUniqueList.contains(d.getSaleListUnique())).collect(Collectors.toList());

        for (CanyinSaleListDetailEntity d : dList) {
            SaleListDetail sld = new SaleListDetail();
            sld.setSaleListDetailId(d.getId());
            sld.setSaleListUnique(main.getSaleListMainUnique());
            sld.setGoodsId(d.getGoodsId());
            sld.setGoodsBarcode(d.getGoodsBarcode());
            sld.setGoodsName(d.getGoodsName());
            sld.setSaleListDetailCount(d.getSaleListDetailCount());
            sld.setSaleListDetailPrice(d.getSaleListDetailPrice());
            sld.setSaleListDetailSubtotal(d.getSaleListDetailSubtotal());

            taxDetailList.add(sld);
        }

        return taxDetailList;
    }

    /**
     * 餐饮支付详情转换
     *
     * @return
     */
    private List<SaleListPayDetail> convertTaxSaleListPayDetailList(CanyinSaleListMainEntity main, List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        List<SaleListPayDetail> taxPayDetailList = new ArrayList<>();
        for (CanyinSaleListMainPayDetailEntity mp : mainPayDetailList) {
            SaleListPayDetail slp = new SaleListPayDetail();
            slp.setSaleListUnique(main.getSaleListMainUnique());
            slp.setPayMethod(payMethodMap.get(String.valueOf(mp.getPayMethod())));
            slp.setPayMoney(mp.getPayMoney());
            slp.setServerType(serverTypeMap.get(String.valueOf(mp.getServerType())));
            slp.setMchId(mp.getMchId());
            slp.setPayTime(mp.getCreateTime());

            taxPayDetailList.add(slp);
        }

        return taxPayDetailList;
    }

    /**
     * 转换退款数据
     *
     * @param shop
     * @param startTime
     * @param endTime
     * @param companyReturnList
     * @return
     */
    private Map<String, BigDecimal> convertReturnList(QueryBindShopDto shop, Date startTime, Date endTime, List<SyncReturnList> companyReturnList) {
        Map<String, BigDecimal> map = new HashMap<>();
        //在线支付退款金额
        map.put("onlineMoney", BigDecimal.ZERO);
        //退款退回的服务费
        map.put("returnServiceFee", BigDecimal.ZERO);

        //查询退款单
        LambdaQueryWrapper<CanyinRetListEntity> retListWrapper = new LambdaQueryWrapper<>();
        retListWrapper.eq(CanyinRetListEntity::getShopUnique, shop.getShopUnique());
        retListWrapper.eq(CanyinRetListEntity::getRetListStatus, "3"); //已完成退款
        retListWrapper.ge(CanyinRetListEntity::getRetbackTime, startTime);
        retListWrapper.lt(CanyinRetListEntity::getRetbackTime, endTime);
        List<CanyinRetListEntity> retList = canyinRetListMapper.selectList(retListWrapper);

        if (ObjectUtil.isEmpty(retList)) return map;

        //退款单号
        List<String> retListUniqueList = retList.stream().map(CanyinRetListEntity::getRetListUnique).collect(Collectors.toList());

        //退款支付信息
        LambdaQueryWrapper<CanyinRetListPayDetailEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.in(CanyinRetListPayDetailEntity::getRetListUnique, retListUniqueList);
        List<CanyinRetListPayDetailEntity> payList = canyinRetListPayDetailMapper.selectList(payWrapper);

        //线上退款
        BigDecimal onlineMoney = payList.stream().filter(p -> !"1".equals(p.getServerType()) && !"6".equals(p.getServerType())).map(CanyinRetListPayDetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal returnServiceFee = BigDecimal.ZERO;

        for (CanyinRetListEntity rt : retList) {
            List<CanyinRetListPayDetailEntity> rtpList = payList.stream().filter(p -> p.getRetListUnique().equals(rt.getRetListUnique())).collect(Collectors.toList());

            SyncReturnList sync = new SyncReturnList();
            ReturnListRemote returnListRemote = convertTaxReturnList(rt, shop, rtpList);
            returnServiceFee = NumberUtil.add(returnServiceFee, returnListRemote.getReturnSaleListServiceFee());
            sync.setReturnList(returnListRemote);
            sync.setReturnListDetailList(new ArrayList<>());
            sync.setPaydetailList(convertTaxReturnPayList(rtpList, rt.getSaleListMainUnique(), shop.getCompanyId()));

            companyReturnList.add(sync);
        }

        map.put("onlineMoney", onlineMoney);
        map.put("returnServiceFee", returnServiceFee);
        return map;
    }

    /**
     * 退款单转换
     *
     * @param rt
     * @return
     */
    private ReturnListRemote convertTaxReturnList(CanyinRetListEntity rt, QueryBindShopDto shop, List<CanyinRetListPayDetailEntity> rtpList) {
        //key:餐饮ret_list_status,value:纳统ret_list_state
        Map<String, String> retStatusMap = new HashMap<>();
        retStatusMap.put("1", "1");
        retStatusMap.put("2", "2");
        retStatusMap.put("3", "3");
        retStatusMap.put("4", "4");
        retStatusMap.put("5", "4");

        ReturnListRemote remote = new ReturnListRemote();
        remote.setCompanyId(shop.getCompanyId());
        remote.setSaleListUnique(rt.getSaleListMainUnique());
        remote.setShopUnique(shop.getShopUnique());
        remote.setRetListDatetime(rt.getCreateTime());
        remote.setRetListState(retStatusMap.get(rt.getRetListStatus()));
        remote.setRetListHandlestate("3");
        remote.setRetListTotalMoney(rt.getRetListMoney());
        remote.setRetBackDatetime(rt.getRetbackTime());
        remote.setRetListUnique(rt.getRetListUnique());
        remote.setRetListReason(rt.getRetListReason());
        remote.setRetListDelfee(BigDecimal.ZERO);
        remote.setRetMoneyType(0);
        BigDecimal serviceFeeRate;
        if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
            serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(1000));
        } else {
            serviceFeeRate = BigDecimal.ZERO;
        }
        final BigDecimal[] totalServiceFee = {BigDecimal.ZERO};
        String[] onlineServerTypeList = {"2", "3", "4", "5"};
        rtpList.stream().filter(v -> ArrayUtil.contains(onlineServerTypeList, v.getServerType())).forEach(r -> {
            BigDecimal serviceFee = NumberUtil.mul(r.getPayMoney(), serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            totalServiceFee[0] = NumberUtil.add(totalServiceFee[0], serviceFee);
        });

        remote.setReturnSaleListServiceFee(totalServiceFee[0]);
        remote.setShopName(shop.getShopName());

        return remote;
    }

    /**
     * 退款数据转换
     *
     * @param rtpList
     * @param saleListMainUnique
     * @return
     */
    private List<ReturnListPaydetailRemote> convertTaxReturnPayList(List<CanyinRetListPayDetailEntity> rtpList, String saleListMainUnique, Long companyId) {
        List<ReturnListPaydetailRemote> retList = new ArrayList<>();

        //key:餐饮server_type,value:纳统server_type
        Map<String, Integer> serverTypeMap = new HashMap<>();
        serverTypeMap.put("1", 1);
        serverTypeMap.put("2", 6);
        serverTypeMap.put("3", 6);
        serverTypeMap.put("4", 6);
        serverTypeMap.put("5", 6);
        serverTypeMap.put("6", 1);

        for (CanyinRetListPayDetailEntity r : rtpList) {
            ReturnListPaydetailRemote p = new ReturnListPaydetailRemote();
            p.setCompanyId(companyId);
            p.setSaleListUnique(saleListMainUnique);
            p.setRetListUnique(r.getRetListUnique());
            p.setPayType(payMethodMap.get(r.getPayMethod()));
            p.setPayMoney(r.getPayMoney());
            p.setServiceType(serverTypeMap.get(r.getServerType()));
            p.setMchId("");
            retList.add(p);
        }

        return retList;
    }

    /**
     * 保存公司退款单
     */
    private void syncCompanyReturnList(Long companyId, List<SyncReturnList> companyReturnList) {
        if (ObjectUtil.isEmpty(companyReturnList)) return;

        SyncReturnListParams params = new SyncReturnListParams();
        params.setReturnList(companyReturnList);
        params.setCompanyId(companyId);
        params.setBusinessType("canyin");

        returnListFacade.syncReturnList(params);
    }

}
