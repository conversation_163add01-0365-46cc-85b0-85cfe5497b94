package cc.buyhoo.tax.task;

import cc.buyhoo.tax.facade.TruncateOrderDataFacade;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class TruncateOrderDataTask {

    @DubboReference
    private TruncateOrderDataFacade truncateOrderDataFacade;

    /**
     * 清空纳统订单相关数据，方便测试人员测试
     */
    @XxlJob("truncateOrderData")
    public void truncateOrderData() {
        truncateOrderDataFacade.truncateOrdeData();
    }

}
