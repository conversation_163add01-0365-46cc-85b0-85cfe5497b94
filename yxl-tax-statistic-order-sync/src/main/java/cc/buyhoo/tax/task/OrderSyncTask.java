package cc.buyhoo.tax.task;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.facade.*;
import cc.buyhoo.tax.facade.enums.BusSyncTypeEnum;
import cc.buyhoo.tax.facade.params.busInventoryOrderCategory.OrderCategoryRemote;
import cc.buyhoo.tax.facade.params.busInventoryOrderCategory.SaveInventoryOrderCategoryRemoteParams;
import cc.buyhoo.tax.facade.params.busInventoryBatch.InventoryBatchDetailRemote;
import cc.buyhoo.tax.facade.params.busInventoryBatch.InventoryBatchRemote;
import cc.buyhoo.tax.facade.params.busInventoryBatch.SaveInventoryBatchRemoteParams;
import cc.buyhoo.tax.facade.params.busInventoryNotSync.InventoryNotSyncInsertBatchDto;
import cc.buyhoo.tax.facade.params.busInventoryNotSync.InventoryNotSyncInsertBatchParams;
import cc.buyhoo.tax.facade.params.busInventoryOrder.SaveInventoryOrderRemoteParams;
import cc.buyhoo.tax.facade.params.busInventoryOrder.UpdateInventoryOrderRemoteParams;
import cc.buyhoo.tax.facade.params.busShop.QueryBindShopParams;
import cc.buyhoo.tax.facade.params.busShopBill.UpdateUnsettledAmountRemoteParams;
import cc.buyhoo.tax.facade.params.busShopInvoice.BusShopInvoiceRemoteParams;
import cc.buyhoo.tax.facade.params.busShopServiceFee.UpdateShopServiceFeeParams;
import cc.buyhoo.tax.facade.params.goods.QueryByGoodsIdDto;
import cc.buyhoo.tax.facade.params.goods.QueryByGoodsIdResult;
import cc.buyhoo.tax.facade.params.returnList.*;
import cc.buyhoo.tax.facade.params.saleList.*;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cc.buyhoo.tax.facade.result.busSyncRecord.BusSyncRecordDto;
import cc.buyhoo.tax.facade.result.busSyncRecord.QueryBusSyncRecordResult;
import cc.buyhoo.tax.facade.result.goods.QueryByGoodsIdParams;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class OrderSyncTask extends AbstractSyncTaskHelper{

    @DubboReference
    private BusShopFacade busShopFacade;

    @DubboReference(retries = 0)
    private SaleListFacade saleListFacade;

    @DubboReference
    private SysCompanyFacade sysCompanyFacade;

    @DubboReference(retries = 0)
    private ReturnListFacade returnListFacade;

    @Resource
    private SaleListMapper saleListMapper;

    @Resource
    private SaleListDetailMapper saleListDetailMapper;

    @Resource
    private SaleListPayDetailMapper saleListPayDetailMapper;

    @Resource
    private ShopCouponCusMapper shopCouponCusMapper;

    @Resource
    private ShopCouponMapper shopCouponMapper;

    @Resource
    private ReturnListMapper returnListMapper;

    @Resource
    private ReturnListDetailMapper returnListDetailMapper;

    @Resource
    private ReturnListPaydetailMapper returnListPaydetailMapper;

    @Resource
    private GoodsMapper goodsMapper;

    @Value("${order.sync.task.pageSize}")
    private Integer orderSyncTaskPageSize;

    @DubboReference
    private BusSyncRecordFacade busSyncRecordFacade;

    @DubboReference
    private BusShopInvoiceFacade busShopInvoiceFacade;

    /**
     * 百货订单同步
     */
    @XxlJob("ShopOrderTest")
    public void ShopOrderTest() {

        QueryBranchCompanyDto comp = new QueryBranchCompanyDto();
        comp.setId(9L);
        QueryBindShopDto shop = busShopFacade.queryByShopUnique(1719044307315L);
        if (ObjectUtil.isNull(shop)) {
            return;
        }
        //按纳统企业统计
        Date startTime = DateUtil.parseDateTime("2024-07-02 17:10:33");
        Date endTime = DateUtil.parseDateTime("2024-07-02 19:30:28");
        try {
            //在线支付金额
            BigDecimal onlinePay = BigDecimal.ZERO;
            //现金支付金额
            BigDecimal cashPay = BigDecimal.ZERO;

            int startSaleListId = 0;
            Integer size = 0;
            //分页查询数据
            while (true) {
                //查询订单数据
                List<SaleListEntity> saleList = handleQuerySaleList(shop.getShopUnique(), startTime, endTime,startSaleListId);
                size += saleList.size();
                //结束循环
                if (ObjectUtil.isEmpty(saleList)) break;
                //继续进行下次分页查询
                startSaleListId = saleList.get(saleList.size() - 1).getSaleListId();
                List<Long> saleListUniqueList = saleList.stream().map(SaleListEntity::getSaleListUnique).collect(Collectors.toList());
                //查询订单详情
                List<SaleListDetailEntity> detailList = selectSaleListDetail(saleListUniqueList);
                //查询支付详情
                List<SaleListPayDetailEntity> payDetailList = selectSaleListPayDetail(saleListUniqueList);
                List<Integer> detailIdList = detailList.stream().map(SaleListDetailEntity::getSaleListDetailId).collect(Collectors.toList());
                //查询详情数量、小计
                List<SaleListDetailTotalEntity> detailTotalList = selectSaleListDetailTotal(detailIdList);
                //同步订单数据
                List<SaveSaleList> saveList = syncSaleList(saleList,shop,detailList,payDetailList,detailTotalList);
                //计算商户支付金额
                Map<String, BigDecimal> onlineCashMap = countMoney(saveList);
                onlinePay = NumberUtil.add(onlineCashMap.get("online"),onlinePay);
                cashPay = NumberUtil.add(onlineCashMap.get("cash"),cashPay);
                //生成发票
                syncShopInvoice(comp.getId(), saveList);
            }
            log.info("------------订单数量：{}--------------", size);
        }catch (Exception e){
            e.printStackTrace();
            XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 参数转换
     * @param shop
     * @param sl
     * @return
     */
    private SaveSaleList convertSaveList(QueryBindShopDto shop,SaleListEntity sl,List<SaleListDetailEntity> detailList,List<SaleListPayDetailEntity> payDetailList,List<SaleListDetailTotalEntity> detailTotalList) {
        SaveSaleList saveSaleList = new SaveSaleList();

        //sale_list_pay_detail
        List<SaleListPayDetailEntity> pdeList = payDetailList.stream().filter(p -> p.getSaleListUnique().equals(sl.getSaleListUnique())).collect(Collectors.toList());

        List<SaleListPayDetail> pdList = new ArrayList<>();
        for (SaleListPayDetailEntity slp : pdeList) {
            SaleListPayDetail pd = new SaleListPayDetail();
            BeanUtils.copy(slp,pd);
            pd.setSaleListUnique(String.valueOf(slp.getSaleListUnique()));
            pdList.add(pd);
        }

        saveSaleList.setSaleListPayDetailList(pdList);

        //计算线上支付金额
        BigDecimal onlinePay = pdeList.stream().filter(p -> p.getServerType() != 0 || p.getPayMethod() == 13).map(SaleListPayDetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

        //sale_list
        SaleList saleList = new SaleList();
        BeanUtils.copy(sl,saleList);
        saleList.setCompanyId(shop.getCompanyId());
        saleList.setShopName(shop.getShopName());
        saleList.setSaleListUnique(String.valueOf(sl.getSaleListUnique()));
        BigDecimal serviceFeeRate = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
            serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(1000));
        }
        BigDecimal serviceFee = NumberUtil.mul(new BigDecimal(String.valueOf(onlinePay)), serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        saleList.setSaleListServiceFee(serviceFee);
        saveSaleList.setSaleList(saleList);

        //sale_list_detail
        List<SaleListDetailEntity> detailEntityList = detailList.stream().filter(d -> d.getSaleListUnique().equals(sl.getSaleListUnique())).collect(Collectors.toList());
        List<SaleListDetail> sldList = new ArrayList<>();
        for (SaleListDetailEntity del : detailEntityList) {
            SaleListDetail sld = new SaleListDetail();
            BeanUtils.copy(del,sld);
            sld.setSaleListUnique(String.valueOf(del.getSaleListUnique()));
            sld.setSaleListDetailId(Long.valueOf(del.getSaleListDetailId()));
            sldList.add(sld);
        }

        for (SaleListDetail sld : sldList) {
            SaleListDetailTotalEntity te = detailTotalList.stream().filter(d -> d.getSaleListDetailId().equals(sld.getSaleListDetailId())).findFirst().orElse(null);
            sld.setSaleListDetailCount(ObjectUtil.isEmpty(te) ? sld.getSaleListDetailCount() : te.getSaleListDetailCount());
            sld.setSaleListDetailSubtotal(ObjectUtil.isEmpty(te) ? sld.getSaleListDetailSubtotal() : te.getSaleListDetailTotal());
        }
        saveSaleList.setSaleListDetailList(sldList);

        return saveSaleList;
    }

    /**
     * 查询订单详情
     * @param saleListUniqueList
     */
    private List<SaleListDetailEntity> selectSaleListDetail(List<Long> saleListUniqueList) {
        LambdaQueryWrapper<SaleListDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(SaleListDetailEntity::getSaleListUnique,saleListUniqueList);
        return saleListDetailMapper.selectList(detailWrapper);
    }

    /**
     * 查询订单支付详情
     * @param saleListUniqueList
     * @return
     */
    private List<SaleListPayDetailEntity> selectSaleListPayDetail(List<Long> saleListUniqueList) {
        LambdaQueryWrapper<SaleListPayDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(SaleListPayDetailEntity::getSaleListUnique,saleListUniqueList);
        return saleListPayDetailMapper.selectList(detailWrapper);
    }

    /**
     * 查询详情数量、小计
     * @return
     */
    private List<SaleListDetailTotalEntity> selectSaleListDetailTotal(List<Integer> detailIdList) {
        if (ObjectUtil.isEmpty(detailIdList)) return new ArrayList<>();
        return saleListDetailMapper.queryDetailTotal(detailIdList);
    }

    /**
     * 统计商户支付金额
     */
    private Map<String,BigDecimal> countMoney(List<SaveSaleList> saveList) {
        Map<String,BigDecimal> onlineCashMap = new HashMap<>();
        List<SaleList> saleList = saveList.stream().map(SaveSaleList::getSaleList).collect(Collectors.toList());

        List<SaleListPayDetail> payDetailList = saveList.stream().map(SaveSaleList::getSaleListPayDetailList).flatMap(f -> f.stream()).collect(Collectors.toList());

        //优惠券逻辑
        Set<Integer> couponCusIdSet = saleList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getShopCouponId()) && s.getCouponAmount().doubleValue() > 0).map(SaleList::getShopCouponId).collect(Collectors.toSet());
        List<ShopCouponEntity> shopCouponList = new ArrayList<>();
        List<ShopCouponCusEntity> couponCusList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(couponCusIdSet)) {
            couponCusList = shopCouponCusMapper.selectBatchIds(couponCusIdSet);
            Set<Integer> couponIdSet = couponCusList.stream().map(ShopCouponCusEntity::getShopCouponId).collect(Collectors.toSet());
            LambdaQueryWrapper<ShopCouponEntity> shopCouponWrapper = new LambdaQueryWrapper<>();
            shopCouponWrapper.select(ShopCouponEntity::getShopCouponId,ShopCouponEntity::getCouponType,ShopCouponEntity::getShopUnique);
            shopCouponWrapper.in(ShopCouponEntity::getShopCouponId,couponIdSet);
            shopCouponList = shopCouponMapper.selectList(shopCouponWrapper);
        }

        Iterator<SaleList> iter = saleList.iterator();
        BigDecimal onlineTotalMoney = BigDecimal.ZERO;
        BigDecimal cashTotalMoney = BigDecimal.ZERO;
        while (iter.hasNext()) {
            SaleList sl = iter.next();

            List<SaleListPayDetail> payList = payDetailList.stream().filter(p -> p.getSaleListUnique().equals(sl.getSaleListUnique())).collect(Collectors.toList());

            BigDecimal onlinePay = BigDecimal.ZERO;
            BigDecimal cashPay = BigDecimal.ZERO;
            //只统计类型为0(实体店销售)、2(微信商城小程序)、7(收银端平台会员结算)
            if (sl.getSaleType() != 0 && sl.getSaleType() != 2 && sl.getSaleType() != 7) iter.remove();
            if (sl.getSaleType() == 0) {
                //实体店销售 取支付方式不是0的，即取在线支付的
                onlinePay = payList.stream().filter(p -> p.getServerType() != 0 || p.getPayMethod() == 13).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                cashPay = payList.stream().filter(p -> p.getServerType() == 0 && p.getPayMethod() != 13).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            }else {
                //使用了平台优惠券
                boolean pltCoupon = false;
                if (ObjectUtil.isNotEmpty(sl.getCouponAmount()) && sl.getCouponAmount().doubleValue() > 0) { //判断下是不是平台优惠券，平台优惠券就需要加到支付金额，否则不需要
                    Integer shopCouponId = couponCusList.stream().filter(c -> c.getShopCouponCusId().equals(sl.getShopCouponId())).map(ShopCouponCusEntity::getShopCouponId).findFirst().orElse(0);
                    if (ObjectUtil.isNotEmpty(shopCouponId) && shopCouponId != 0) {
                        //8302016134121平台券
                        Integer couponId = shopCouponList.stream().filter(s -> s.getShopCouponId().equals(shopCouponId) && "8302016134121".equals(String.valueOf(s.getShopUnique()))).map(ShopCouponEntity::getShopCouponId).findFirst().orElse(0);
                        if (couponId > 0) {
                            pltCoupon = true;
                        }
                    }
                }
                //使用了平台优惠券支付金额为：优惠券+实付，否则就是实付
                onlinePay = pltCoupon ? NumberUtil.add(sl.getSaleListActuallyReceived(),sl.getCouponAmount()) : sl.getSaleListActuallyReceived();
            }
            onlineTotalMoney = NumberUtil.add(onlineTotalMoney,onlinePay);
            cashTotalMoney = NumberUtil.add(cashTotalMoney,cashPay);
        }

        onlineCashMap.put("online",onlineTotalMoney);
        onlineCashMap.put("cash",cashTotalMoney);

        return onlineCashMap;
    }


    /**
     * 查询订单
     * @param shopUnique
     * @param startTime
     * @param endTime
     */
    private List<SaleListEntity> handleQuerySaleList(Long shopUnique,Date startTime, Date endTime,int saleListId) {
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("dateStart",startTime);
        queryMap.put("dateEnd",endTime);
        queryMap.put("shopUnique",shopUnique);
        queryMap.put("saleListId",saleListId);
        queryMap.put("pageSize",orderSyncTaskPageSize);
        return saleListMapper.querySaleListPage(queryMap);
    }

    /**
     * 同步订单相关信息
     */
    private List<SaveSaleList> syncSaleList(List<SaleListEntity> saleList,QueryBindShopDto shop,List<SaleListDetailEntity> detailList,List<SaleListPayDetailEntity> payDetailList,List<SaleListDetailTotalEntity> detailTotalList) {
        //参数构建
        SaveSaleListParams params = new SaveSaleListParams();
        List<SaveSaleList> saveList = new ArrayList<>();
        for (SaleListEntity sl : saleList) {
            saveList.add(convertSaveList(shop,sl,detailList,payDetailList,detailTotalList));
        }

        params.setSaveList(saveList);
        //保存数据
        saleListFacade.saveSaleList(params);

        return saveList;
    }

    /**
     * 退款订单数据转换
     * @param companyId
     * @param shop
     * @param startTime
     * @param endTime
     * @param companyReturnList
     * @return 退款单的线上支付金额
     */
    private Map<String,BigDecimal> convertShopReturnList(Long companyId,QueryBindShopDto shop, Date startTime, Date endTime, List<SyncReturnList> companyReturnList) {
        Map<String,BigDecimal> resp = new HashMap<>();
        //在线支付退款金额
        resp.put("onlineMoney",BigDecimal.ZERO);
        //退款退回的服务费
        resp.put("returnServiceFee",BigDecimal.ZERO);

        //查询退款单
        LambdaQueryWrapper<ReturnListEntity> returnListWrapper = new LambdaQueryWrapper<>();
        returnListWrapper.eq(ReturnListEntity::getShopUnique,shop.getShopUnique());
        returnListWrapper.eq(ReturnListEntity::getRetListState,2); //已退款
        returnListWrapper.eq(ReturnListEntity::getRetListHandlestate,3); //受理完毕
        returnListWrapper.ge(ReturnListEntity::getRetBackDatetime,startTime);
        returnListWrapper.lt(ReturnListEntity::getRetBackDatetime,endTime);
        List<ReturnListEntity> returnList = returnListMapper.selectList(returnListWrapper);

        if (ObjectUtil.isEmpty(returnList)) return resp;

        //退款单号
        List<String> retSaleList = returnList.stream().map(ReturnListEntity::getRetListUnique).collect(Collectors.toList());

        //查询退款单详情
        LambdaQueryWrapper<ReturnListDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(ReturnListDetailEntity::getRetListUnique,retSaleList);
        List<ReturnListDetailEntity> detailList = returnListDetailMapper.selectList(detailWrapper);

        //根据条码查询商品id
        List<String> goodsBarcodeList = detailList.stream().map(ReturnListDetailEntity::getGoodsBarcode).collect(Collectors.toList());
        LambdaQueryWrapper<GoodsEntity> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.eq(GoodsEntity::getShopUnique,shop.getShopUnique());
        goodsWrapper.in(GoodsEntity::getGoodsBarcode,goodsBarcodeList);
        List<GoodsEntity> goodsList = goodsMapper.selectList(goodsWrapper);
        Map<String, Integer> goodsBarcodeGoodsIdMap = goodsList.stream().collect(Collectors.toMap(GoodsEntity::getGoodsBarcode, GoodsEntity::getGoodsId));

        //查询退款单支付详情
        LambdaQueryWrapper<ReturnListPaydetailEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.in(ReturnListPaydetailEntity::getRetListUnique,retSaleList);
        List<ReturnListPaydetailEntity> payList = returnListPaydetailMapper.selectList(payWrapper);

        //线上退款
        BigDecimal onlineMoney = payList.stream().filter(p -> p.getServiceType() != 1).map(ReturnListPaydetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal returnServiceFee = BigDecimal.ZERO;
        //退款单参数
        for (ReturnListEntity rl : returnList) {
            SyncReturnList sync = new SyncReturnList();

            //退款支付详情
            List<ReturnListPaydetailEntity> rlpList = payList.stream().filter(p -> p.getRetListUnique().equals(rl.getRetListUnique())).collect(Collectors.toList());

            //退款单
            ReturnListRemote returnListRemote = new ReturnListRemote();
            BeanUtils.copy(rl,returnListRemote);
            returnListRemote.setCompanyId(companyId);
            returnListRemote.setShopUnique(Long.parseLong(rl.getShopUnique()));
            returnListRemote.setShopName(shop.getShopName());
            BigDecimal serviceFee = rlpList.stream().filter(p -> p.getServiceType() != 1).map(ReturnListPaydetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal serviceFeeRate = BigDecimal.ZERO;
            if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
                serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(1000));
            }
            serviceFee = NumberUtil.mul(serviceFee, serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            returnListRemote.setReturnSaleListServiceFee(serviceFee);

            returnServiceFee = NumberUtil.add(returnServiceFee,serviceFee);

            //退款单详情
            List<ReturnListDetailEntity> rldList = detailList.stream().filter(d -> d.getRetListUnique().equals(rl.getRetListUnique())).collect(Collectors.toList());
            List<ReturnListDetailRemote> returnListDetailRemoteList = BeanUtils.copyList(rldList, ReturnListDetailRemote.class);
            for (ReturnListDetailRemote rld : returnListDetailRemoteList) {
                Integer goodsId = goodsBarcodeGoodsIdMap.get(rld.getGoodsBarcode());
                rld.setGoodsId(ObjectUtil.isEmpty(goodsId) ? 0L : goodsId);
            }
            List<ReturnListPaydetailRemote> returnListPaydetailRemoteList = BeanUtils.copyList(rlpList, ReturnListPaydetailRemote.class);

            sync.setReturnList(returnListRemote);
            sync.setReturnListDetailList(returnListDetailRemoteList);
            sync.setPaydetailList(returnListPaydetailRemoteList);
            companyReturnList.add(sync);
        }

        //在线支付退款金额
        resp.put("onlineMoney",onlineMoney);
        //退款退回的服务费
        resp.put("returnServiceFee",returnServiceFee);

        return resp;
    }

    /**
     * 保存公司退款单
     */
    private void syncCompanyReturnList(Long companyId,List<SyncReturnList> companyReturnList) {
        if (ObjectUtil.isEmpty(companyReturnList)) return;

        SyncReturnListParams params = new SyncReturnListParams();
        params.setReturnList(companyReturnList);
        params.setCompanyId(companyId);
        params.setBusinessType("cash");

        returnListFacade.syncReturnList(params);
    }


    /**
     * 自动生成开票服务
     * @param companyId
     * @param saleList
     */
    private void syncShopInvoice(Long companyId, List<SaveSaleList> saleList) {
        BusShopInvoiceRemoteParams params = new BusShopInvoiceRemoteParams();
        params.setCompanyId(companyId);
        params.setSaleListList(saleList);
        busShopInvoiceFacade.autoInsertShopInvoice(params);
    }
}
