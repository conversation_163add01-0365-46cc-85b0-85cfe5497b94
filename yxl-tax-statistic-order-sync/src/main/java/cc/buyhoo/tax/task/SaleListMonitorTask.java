package cc.buyhoo.tax.task;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.CanyinRetListEntity;
import cc.buyhoo.tax.entity.CanyinRetListPayDetailEntity;
import cc.buyhoo.tax.entity.CanyinSaleListMainEntity;
import cc.buyhoo.tax.facade.*;
import cc.buyhoo.tax.facade.params.busShop.QueryBindShopParams;
import cc.buyhoo.tax.facade.params.busShopSaleListMonitor.*;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import cc.buyhoo.tax.result.SelectBuyhooReturnListInfoDto;
import cc.buyhoo.tax.result.SelectBuyhooSaleListInfoDto;
import cc.buyhoo.tax.result.SelectCanyinReturnListInfoDto;
import cc.buyhoo.tax.result.SelectCanyinSaleListInfoDto;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class SaleListMonitorTask {

    @DubboReference
    private SysCompanyFacade sysCompanyFacade;

    @DubboReference
    private BusShopFacade busShopFacade;

    @DubboReference
    private BusShopSaleListMonitorFacade busShopSaleListMonitorFacade;

    @Resource
    private SaleListMapper saleListMapper;

    @Resource
    private ReturnListMapper returnListMapper;

    @Resource
    private CanyinSaleListMainMapper canyinSaleListMainMapper;

    @Resource
    private CanyinRetListMapper canyinRetListMapper;

    @Resource
    private CanyinRetListPayDetailMapper canyinRetListPayDetailMapper;
    /**
     * 订单监控
     */
    @XxlJob("saleListMonitorJobHandler")
    public void saleListMonitor() {
        Date endTime = DateUtil.endOfDay(DateUtil.yesterday());
        Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());
        //查询分公司
        Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
        if (branchCompResult.hasFail()) {
            XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
            return;
        }
        List<QueryBranchCompanyDto> companyList = branchCompResult.getData().getCompanyList();
        List<BusSaleListMonitorUpdateParams> busSaleListMonitorUpdateParamsList = new ArrayList<>();
        Long syncBuyhooCount = 0L;
        Long syncCanyinCount = 0L;
        BigDecimal syncBuyhooAmount = BigDecimal.ZERO;
        BigDecimal syncCanyinAmount = BigDecimal.ZERO;
        BusSaleListMonitorUpdateListParams listParams = new BusSaleListMonitorUpdateListParams();
        listParams.setStatDate(DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd"));
        listParams.setStartSyncTime(startTime);
        listParams.setEndSyncTime(endTime);
        List<BusSaleListMonitorSyncParams> syncList = new ArrayList<>();
        //按纳统企业统计
        for (QueryBranchCompanyDto comp : companyList) {
            if (ObjectUtil.isNotNull(comp.getStatisticsStatus()) && ObjectUtil.equals(1, comp.getStatisticsStatus())) {
                //查询所有的店铺
                Result<QueryBindShopResult> shopResult = queryBindShops(comp.getId());
                if (shopResult.hasFail()) {
                    XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
                    continue;
                }
                Result<QueryBindShopResult> canyinShopResult = queryBindCanyinShops(comp.getId());
                //百货店铺列表
                List<QueryBindShopDto> shopList = shopResult.getData().getShopList();
                shopList.addAll(canyinShopResult.getData().getShopList());
                for (QueryBindShopDto shop : shopList) {
                    BusSaleListMonitorSyncParams syncParams = new BusSaleListMonitorSyncParams();
                    syncParams.setShopUnique(shop.getShopUnique());
                    syncParams.setShopName(shop.getShopName());
                    // 同步零售订单列表对比
                    syncParams.setSaleListUniqueList(Collections.EMPTY_LIST);
                    // 同步餐饮订单列表对比
                    syncParams.setReturnListUniqueList(Collections.EMPTY_LIST);
                    BusSaleListMonitorUpdateParams params = new BusSaleListMonitorUpdateParams();
                    params.setShopUnique(shop.getShopUnique());
                    params.setStatDate(DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd"));
                    List<BusSaleListMonitorSettledParams> saleListMonitorSettledParamsListAll = new ArrayList<>();
                    List<BusSaleListMonitorSettledReturnParams> saleListMonitorSettledReturnParamsListAll = new ArrayList<>();
                    //同步零售订单数据
                    List<SelectBuyhooSaleListInfoDto> selectBuyhooSyncSaleListInfoDtoList = saleListMapper.selectBuyhooSaleListInfoCash(shop.getShopUnique(), startTime, endTime);
                    if (ObjectUtil.isNotEmpty(selectBuyhooSyncSaleListInfoDtoList)) {
                        //同步零售订单
                        List<BusSaleListMonitorSettledParams> buyhooSaleListMonitorSettledParamsList = selectBuyhooSyncSaleListInfoDtoList.stream().map(selectBuyhooSyncSaleListInfoDto -> {
                            BusSaleListMonitorSettledParams buyhooSaleListMonitorSettledParams = new BusSaleListMonitorSettledParams();
                            buyhooSaleListMonitorSettledParams.setSaleListUnique(selectBuyhooSyncSaleListInfoDto.getSaleListUnique());
                            buyhooSaleListMonitorSettledParams.setSettledFlag(false);
                            return buyhooSaleListMonitorSettledParams;
                        }).collect(Collectors.toList());
                        saleListMonitorSettledParamsListAll.addAll(buyhooSaleListMonitorSettledParamsList);
                        syncBuyhooCount = syncBuyhooCount + Long.valueOf(selectBuyhooSyncSaleListInfoDtoList.size());
                        syncBuyhooAmount = syncBuyhooAmount.add(selectBuyhooSyncSaleListInfoDtoList.stream().map(SelectBuyhooSaleListInfoDto::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    //结算零售数据
                    List<SelectBuyhooSaleListInfoDto> selectBuyhooSaleListInfoDtoList = saleListMapper.selectBuyhooSaleListInfo(shop.getShopUnique(), startTime, endTime);
                    List<SelectBuyhooReturnListInfoDto> selectBuyhooSyncReturnListInfoDtoList = returnListMapper.selectBuyhooReturnListInfoCash(shop.getShopUnique(), startTime, endTime);
                    List<SelectBuyhooReturnListInfoDto> selectBuyhooReturnListInfoDtoList = returnListMapper.selectBuyhooReturnListInfo(shop.getShopUnique(), startTime, endTime);

                    if (ObjectUtil.isNotEmpty(selectBuyhooSyncReturnListInfoDtoList)) {
                        //同步零售退单
                        List<BusSaleListMonitorSettledReturnParams> busSaleListMonitorSettledReturnParamsList = selectBuyhooSyncReturnListInfoDtoList.stream().map(selectBuyhooReturnListInfoDto -> {
                            BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams = new BusSaleListMonitorSettledReturnParams();
                            busSaleListMonitorSettledReturnParams.setSaleListUnique(selectBuyhooReturnListInfoDto.getSaleListUnique());
                            busSaleListMonitorSettledReturnParams.setSettledFlag(false);
                            return busSaleListMonitorSettledReturnParams;
                        }).collect(Collectors.toList());
                        saleListMonitorSettledReturnParamsListAll.addAll(busSaleListMonitorSettledReturnParamsList);
                    }

                    if (ObjectUtil.isNotEmpty(selectBuyhooReturnListInfoDtoList)) {
                        params.setBuyhooReturnCount(Long.valueOf(selectBuyhooReturnListInfoDtoList.size()));
                    } else {
                        params.setBuyhooReturnCount(0L);
                    }
                    if (ObjectUtil.isNotEmpty(selectBuyhooSaleListInfoDtoList)) {
                        params.setBuyhooSaleCount(Long.valueOf(selectBuyhooSaleListInfoDtoList.size()));
                        BigDecimal buyhooAmount = selectBuyhooSaleListInfoDtoList.stream().map(v -> {
                            BigDecimal serviceFeeRate = BigDecimal.ZERO;
                            if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
                                serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(100));
                            }
                            BigDecimal saleListAmount = v.getPayMoney().subtract(v.getPayMoney().multiply(serviceFeeRate).setScale(2, RoundingMode.HALF_UP));
                            if (ObjectUtil.isNotEmpty(selectBuyhooReturnListInfoDtoList)
                                    && selectBuyhooReturnListInfoDtoList.stream().anyMatch(r -> r.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                BigDecimal returnListAmount = selectBuyhooReturnListInfoDtoList.stream().filter(r -> r.getSaleListUnique().equals(v.getSaleListUnique())).findFirst().get().getPayMoney();
                                if (v.getPayMoney().compareTo(returnListAmount) == 0) {
                                    return BigDecimal.ZERO;
                                } else {
                                    saleListAmount = saleListAmount.subtract(returnListAmount.add(returnListAmount.multiply(shop.getServiceFeeRate()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                                    return saleListAmount;
                                }
                            }
                            return saleListAmount;
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                        params.setBuyhooAmount(buyhooAmount);
                    } else {
                        params.setBuyhooAmount(BigDecimal.ZERO);
                        params.setBuyhooSaleCount(0L);
                    }

                    //餐饮数据
                    Map<String, Object> queryMap = new HashMap<>();
                    queryMap.put("dateStart", startTime);
                    queryMap.put("dateEnd", endTime);
                    queryMap.put("shopUnique", shop.getShopUnique());
                    List<SelectCanyinSaleListInfoDto> canyinSaleListMainEntityList = canyinSaleListMainMapper.selectCanyinSaleListInfoCash(queryMap);
                    if (ObjectUtil.isNotEmpty(canyinSaleListMainEntityList)) {
                        List<BusSaleListMonitorSettledParams> canyinSaleListMonitorSettledParamsList = canyinSaleListMainEntityList.stream().map(canyinSaleListMainEntityDto -> {
                            BusSaleListMonitorSettledParams buyhooSaleListMonitorSettledParams = new BusSaleListMonitorSettledParams();
                            buyhooSaleListMonitorSettledParams.setSaleListUnique(canyinSaleListMainEntityDto.getSaleListUnique());
                            buyhooSaleListMonitorSettledParams.setSettledFlag(false);
                            return buyhooSaleListMonitorSettledParams;
                        }).collect(Collectors.toList());
                        saleListMonitorSettledParamsListAll.addAll(canyinSaleListMonitorSettledParamsList);
                        syncCanyinCount = syncCanyinCount + Long.valueOf(canyinSaleListMainEntityList.size());
                        syncCanyinAmount = syncCanyinAmount.add(canyinSaleListMainEntityList.stream().map(SelectCanyinSaleListInfoDto::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }

                    syncParams.setSaleListUniqueList(saleListMonitorSettledParamsListAll);

                    if (ObjectUtil.isNotEmpty(selectBuyhooSaleListInfoDtoList)) {
                        // 结算零售订单
                        syncParams.getSaleListUniqueList().stream().forEach(v -> {
                            if (selectBuyhooSaleListInfoDtoList.stream().anyMatch(s -> s.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                v.setSettledFlag(true);
                            }
                        });
                    }
                    List<SelectCanyinSaleListInfoDto> canyinSaleListMainEntityListCash = canyinSaleListMainMapper.selectCanyinSaleListInfo(queryMap);

                    //查询退款单
                    LambdaQueryWrapper<CanyinRetListEntity> retListWrapper = new LambdaQueryWrapper<>();
                    retListWrapper.eq(CanyinRetListEntity::getShopUnique, shop.getShopUnique());
                    retListWrapper.eq(CanyinRetListEntity::getRetListStatus, "3"); //已完成退款
                    retListWrapper.ge(CanyinRetListEntity::getRetbackTime, startTime);
                    retListWrapper.lt(CanyinRetListEntity::getRetbackTime, endTime);
                    List<CanyinRetListEntity> retList = canyinRetListMapper.selectList(retListWrapper);
                    List<SelectCanyinReturnListInfoDto> selectCanyinReturnListInfoDtoList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(retList)) {
                        //同步零售退单
                        List<BusSaleListMonitorSettledReturnParams> busSaleListMonitorSettledReturnParamsList = retList.stream().map(retDto -> {
                            BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams = new BusSaleListMonitorSettledReturnParams();
                            busSaleListMonitorSettledReturnParams.setSaleListUnique(retDto.getSaleListMainUnique());
                            busSaleListMonitorSettledReturnParams.setSettledFlag(false);
                            return busSaleListMonitorSettledReturnParams;
                        }).collect(Collectors.toList());
                        saleListMonitorSettledReturnParamsListAll.addAll(busSaleListMonitorSettledReturnParamsList);
                        selectCanyinReturnListInfoDtoList = canyinRetListMapper.selectCanyinReturnListInfo(shop.getShopUnique(), startTime, endTime);
                        params.setCanyinReturnCount(Long.valueOf(selectCanyinReturnListInfoDtoList.size()));
                    } else {
                        params.setCanyinReturnCount(0L);
                    }

                    syncParams.setReturnListUniqueList(saleListMonitorSettledReturnParamsListAll);
                    if (ObjectUtil.isNotEmpty(selectBuyhooReturnListInfoDtoList)) {
                        // 结算零售退单
                        syncParams.getReturnListUniqueList().stream().forEach(v -> {
                            if (selectBuyhooReturnListInfoDtoList.stream().anyMatch(s -> s.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                v.setSettledFlag(true);
                            }
                        });
                    }
                    if (ObjectUtil.isNotEmpty(canyinSaleListMainEntityListCash)) {
                        // 结算餐饮订单
                        syncParams.getSaleListUniqueList().stream().forEach(v -> {
                            if (canyinSaleListMainEntityListCash.stream().anyMatch(s -> s.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                v.setSettledFlag(true);
                            }
                        });
                        params.setCanyinSaleCount(Long.valueOf(canyinSaleListMainEntityListCash.size()));
                        List<SelectCanyinReturnListInfoDto> finalSelectCanyinReturnListInfoDtoList = selectCanyinReturnListInfoDtoList;
                        if (ObjectUtil.isNotEmpty(selectCanyinReturnListInfoDtoList)) {
                            // 结算餐饮退单
                            syncParams.getReturnListUniqueList().stream().forEach(v -> {
                                if (finalSelectCanyinReturnListInfoDtoList.stream().anyMatch(s -> s.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                    v.setSettledFlag(true);
                                }
                            });
                        }
                        BigDecimal buyhooAmount = canyinSaleListMainEntityListCash.stream().map(v -> {
                            BigDecimal serviceFeeRate = BigDecimal.ZERO;
                            if (BigDecimal.ZERO.compareTo(shop.getServiceFeeRate()) == -1) {
                                serviceFeeRate = NumberUtil.div(shop.getServiceFeeRate(), BigDecimal.valueOf(100));
                            }
                            BigDecimal saleListAmount = v.getPayMoney().subtract(v.getPayMoney().multiply(serviceFeeRate).setScale(2, RoundingMode.HALF_UP));
                            if (ObjectUtil.isNotEmpty(finalSelectCanyinReturnListInfoDtoList)
                                    && finalSelectCanyinReturnListInfoDtoList.stream().anyMatch(r -> r.getSaleListUnique().equals(v.getSaleListUnique()))) {
                                BigDecimal returnListAmount = finalSelectCanyinReturnListInfoDtoList.stream().filter(r -> r.getSaleListUnique().equals(v.getSaleListUnique())).findFirst().get().getPayMoney();
                                if (v.getPayMoney().compareTo(returnListAmount) == 0) {
                                    return BigDecimal.ZERO;
                                } else {
                                    saleListAmount = saleListAmount.subtract(returnListAmount.add(returnListAmount.multiply(shop.getServiceFeeRate()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                                    return saleListAmount;
                                }
                            }
                            return saleListAmount;
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                        params.setCanyinAmount(buyhooAmount);
                    } else {
                        params.setCanyinAmount(BigDecimal.ZERO);
                        params.setCanyinSaleCount(0L);
                    }

                    XxlJobHelper.log("监控数据：" + JSONUtil.toJsonStr(params));
                    busSaleListMonitorUpdateParamsList.add(params);
                    syncList.add(syncParams);
                }
            }
        }
        if (ObjectUtil.isNotEmpty(busSaleListMonitorUpdateParamsList)) {
            listParams.setSyncBuyhooCount(syncBuyhooCount);
            listParams.setSyncCanyinCount(syncCanyinCount);
            listParams.setSyncBuyhooAmount(syncBuyhooAmount);
            listParams.setSyncCanyinAmount(syncCanyinAmount);
            listParams.setList(busSaleListMonitorUpdateParamsList);
            listParams.setSyncList(syncList);
            ThreadUtil.execAsync(() -> busShopSaleListMonitorFacade.updateSaleListMonitor(listParams));
        }
    }

    /**
     * 查询公司绑定的店铺
     *
     * @param companyId
     * @return
     */
    private Result<QueryBindShopResult> queryBindShops(Long companyId) {
        //查询所有的店铺
        QueryBindShopParams shopParams = new QueryBindShopParams();
        shopParams.setCompanyId(companyId);
        shopParams.setSyncBuyhooData(Integer.valueOf(1));
        return busShopFacade.queryBindShopByCompany(shopParams);
    }

    /**
     * 查询公司绑定的店铺
     *
     * @param companyId
     * @return
     */
    private Result<QueryBindShopResult> queryBindCanyinShops(Long companyId) {
        //查询所有的店铺
        QueryBindShopParams shopParams = new QueryBindShopParams();
        shopParams.setCompanyId(companyId);
        shopParams.setSyncCanyinData(Integer.valueOf(1));
        return busShopFacade.queryBindShopByCompany(shopParams);
    }
}
