package cc.buyhoo.tax.task;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.facade.BusShopBillFacade;
import cc.buyhoo.tax.facade.SysCompanyFacade;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * @Description 服务费、结算统计
 * @ClassName BillStatisticTask
 * <AUTHOR>
 * @Date 2024/6/13 19:43
 **/
@Service
@RefreshScope
public class BillStatisticTask {

    @DubboReference(retries = 0)
    private BusShopBillFacade busShopBillFacade;

    @DubboReference
    private SysCompanyFacade sysCompanyFacade;

    @XxlJob("billStatisticJobHandler")
    public void billStatistic() {
        String params = XxlJobHelper.getJobParam();
        Date startDate;
        Date endDate;
        if (ObjectUtil.isNotEmpty(params)) {
            String[] paramsArray = params.split(",");
            startDate = DateUtil.beginOfDay(DateUtil.parse(paramsArray[0])).toJdkDate();
            endDate = DateUtil.endOfDay(DateUtil.parse(paramsArray[1])).toJdkDate();
            XxlJobHelper.log("服务费、结算统计时间区间:" + "[" + params + "]");
        } else {
            endDate = null;
            startDate = null;
        }
        //查询分公司
        Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
        if (branchCompResult.hasFail()) {
            XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
            return;
        }
        List<QueryBranchCompanyDto> companyList = branchCompResult.getData().getCompanyList();

        //按纳统企业统计
        for (QueryBranchCompanyDto comp : companyList) {
            ThreadUtil.execAsync(() -> busShopBillFacade.statisticBill(comp.getId(), startDate, endDate));
        }
    }
}
