package cc.buyhoo.tax.task;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.facade.*;
import cc.buyhoo.tax.facade.params.busShop.QueryBindShopParams;
import cc.buyhoo.tax.facade.params.busShopBill.UpdateUnsettledAmountRemoteParams;
import cc.buyhoo.tax.facade.params.busShopServiceFee.UpdateShopServiceFeeParams;
import cc.buyhoo.tax.facade.params.returnList.ReturnListPaydetailRemote;
import cc.buyhoo.tax.facade.params.returnList.ReturnListRemote;
import cc.buyhoo.tax.facade.params.returnList.SyncReturnList;
import cc.buyhoo.tax.facade.params.returnList.SyncReturnListParams;
import cc.buyhoo.tax.facade.params.saleList.*;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

//@Service
//@RefreshScope
public class CanyinOrderSyncTask extends AbstractSyncTaskHelper{

    @DubboReference
    private SysCompanyFacade sysCompanyFacade;

    @DubboReference
    private BusShopFacade busShopFacade;

    @DubboReference
    private SaleListFacade saleListFacade;

    @DubboReference
    private BusShopBillFacade busShopBillFacade;

    @DubboReference
    private BusShopServiceFeeFacade busShopServiceFeeFacade;

    @DubboReference
    private ReturnListFacade returnListFacade;

    @Autowired
    private CanyinSaleListMainMapper saleListMainMapper;

    @Autowired
    private CanyinSaleListMapper saleListMapper;

    @Autowired
    private CanyinSaleListDetailMapper saleListDetailMapper;

    @Autowired
    private CanyinSaleListMainPayDetailMapper mainPayDetailMapper;

    @Autowired
    private CanyinRetListMapper retListMapper;

    @Autowired
    private CanyinRetListPayDetailMapper retListPayDetailMapper;

    @Value("${order.sync.task.pageSize}")
    private Integer orderSyncTaskPageSize;

//    @XxlJob("canyinOrderSync")
    public void canyinOrderSync() {
        String jobParam = XxlJobHelper.getJobParam();

        //查询分公司
        Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
        if (branchCompResult.hasFail()) {
            XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
            return;
        }
        List<QueryBranchCompanyDto> companyList = branchCompResult.getData().getCompanyList();

        //按纳统企业统计
        for (QueryBranchCompanyDto comp : companyList) {
            //查询所有的店铺
            Result<QueryBindShopResult> shopResult = queryBindShops(comp.getId());
            if (shopResult.hasFail()) {
                XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
                continue;
            }

            //退单列表
            List<SyncReturnList> companyReturnList = new ArrayList<>();
            //店铺列表
            List<QueryBindShopDto> shopList = shopResult.getData().getShopList();
            shopList = shopList.stream().filter(s -> s.getShopType() == 12).collect(Collectors.toList());
            //采购单总金额
            BigDecimal inventoryTotalCashMoney = BigDecimal.ZERO;
            BigDecimal inventoryTotalOnlineMoney = BigDecimal.ZERO;
            for (QueryBindShopDto shop : shopList) {
                try {
                    //服务费总额
                    BigDecimal totalServiceFee = BigDecimal.ZERO;
                    //在线支付金额
                    BigDecimal onlinePay = BigDecimal.ZERO;
                    //现金支付金额
                    BigDecimal cashPay = BigDecimal.ZERO;
                    //订单开始id
                    long startSaleListMainId = 0;
                    while (true) {

                        //查询订单数据
                        List<CanyinSaleListMainEntity> saleListMainList = selectQueryCanyinSaleListMain(shop.getShopUnique(),jobParam,startSaleListMainId);
                        //结束循环
                        if (ObjectUtil.isEmpty(saleListMainList)) break;
                        //继续进行下次分页查询
                        startSaleListMainId = saleListMainList.get(saleListMainList.size() - 1).getId();

                        List<String> mainUniqueList = saleListMainList.stream().map(CanyinSaleListMainEntity::getSaleListMainUnique).collect(Collectors.toList());

                        //查询canyin_sale_list
                        List<CanyinSaleListEntity> saleList = selectCanyinSaleList(mainUniqueList);

                        List<String> saleListUniqueList = saleList.stream().map(CanyinSaleListEntity::getSaleListUnique).collect(Collectors.toList());
                        //查询canyin_sale_list_detail
                        List<CanyinSaleListDetailEntity> detailList = selectCanyinSaleListDetail(saleListUniqueList);

                        //查询支付信息
                        List<CanyinSaleListMainPayDetailEntity> mainPayDetailList = selectMainPayDetail(mainUniqueList);

                        //订单同步
                        List<SaveSaleList> saveList = syncSaleList(shop, saleListMainList, saleList, detailList, mainPayDetailList);

                        //计算服务费
                        BigDecimal serviceFee = saveList.stream().map(SaveSaleList::getSaleList).collect(Collectors.toList()).stream().map(SaleList::getSaleListServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalServiceFee = NumberUtil.add(totalServiceFee,serviceFee);

                        //计算线上支付
                        List<SaleListPayDetail> payList = saveList.stream().map(SaveSaleList::getSaleListPayDetailList).flatMap(p -> p.stream()).collect(Collectors.toList());
                        BigDecimal olMoney = payList.stream().filter(p -> p.getServerType() != 1 && p.getServerType() != 6).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal cashMoney = payList.stream().filter(p -> p.getServerType() == 1 || p.getServerType() == 6).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                        onlinePay = NumberUtil.add(onlinePay,olMoney);
                        cashPay = NumberUtil.add(cashPay,cashMoney);
                    }

                    //订单金额-更新商户结算金额
                    updateUnsettledAmount(shop.getShopUnique(), comp.getId(), onlinePay,1);

                    //退款数据处理
                    Map<String, BigDecimal> returnMap = convertReturnList(shop, jobParam, companyReturnList);
                    //在线支付退款金额
                    BigDecimal returnOnlineMoney = returnMap.get("onlineMoney");
                    //退款退回的服务费
                    BigDecimal returnServiceFee = returnMap.get("returnServiceFee");

                    //退款-更新商户结算金额
                    updateUnsettledAmount(shop.getShopUnique(), comp.getId(), returnOnlineMoney,2);

                    //更新店铺的手续费
                    totalServiceFee = NumberUtil.sub(totalServiceFee,returnServiceFee);
                    updateServiceFee(comp.getId(),totalServiceFee,shop);

                    //增加企业统计金额
                    inventoryTotalCashMoney = NumberUtil.add(inventoryTotalCashMoney,cashPay);
                    inventoryTotalOnlineMoney = NumberUtil.add(inventoryTotalOnlineMoney,onlinePay);
                }catch (Exception e){
                    e.printStackTrace();
                    XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
                }
            }

            //根据公司保存退款单
            syncCompanyReturnList(comp.getId(), companyReturnList);
            //保存采购信息，餐饮的只需要保存inventory_order即可，只做首页统计使用
            saveInventoryOrder(comp.getId(),2,inventoryTotalCashMoney,inventoryTotalOnlineMoney);
        }
    }

    /**
     * 查询公司绑定的店铺
     * @param companyId
     * @return
     */
    private Result<QueryBindShopResult> queryBindShops(Long companyId) {
        //查询所有的店铺
        QueryBindShopParams shopParams = new QueryBindShopParams();
        shopParams.setCompanyId(companyId);
        return busShopFacade.queryBindShopByCompany(shopParams);
    }

    /**
     * 日期处理
     * @return
     */
    private Map<String,String> handleDate(String jobParams) {
        Map<String,String> map  = new HashMap<>();

        //{"dataStart":"2023-05-20 00:00:00","dateEnd":"2023-07-25 23:59:59"}
        if (!ObjectUtil.isEmpty(jobParams)){
            try {
                JSONObject paramObj = JSON.parseObject(jobParams);
                if (paramObj.containsKey("dateStart") && paramObj.containsKey("dateEnd")) {
                    map.put("dateStart",paramObj.getString("dateStart"));
                    map.put("dateEnd",paramObj.getString("dateEnd"));
                    return map;
                }
            }catch (Exception e){ //格式错了的话不予执行
                throw new RuntimeException();
            }
        }

        return handlePredayDate();
    }
    private Map<String,String> handlePredayDate() {
        Map<String,String> map  = new HashMap<>();
        Date curr = new Date();
        DateTime dateTime = DateUtil.offsetDay(curr, -1);
        String preDayStr = DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN);
        String dateStart = StringUtils.join(preDayStr, " 00:00:00");
        String dateEnd = StringUtils.join(preDayStr, " 23:59:59");
        map.put("dateStart",dateStart);
        map.put("dateEnd",dateEnd);

        return map;
    }

    /**
     * 查询主订单
     * @param shopUnique
     * @param jobParam
     * @param startSaleListMainId
     * @return
     */
    private List<CanyinSaleListMainEntity> selectQueryCanyinSaleListMain(Long shopUnique,String jobParam,Long startSaleListMainId) {
        Map<String, String> dateMap = handleDate(jobParam);

        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("dateStart",dateMap.get("dateStart"));
        queryMap.put("dateEnd",dateMap.get("dateEnd"));
        queryMap.put("shopUnique",shopUnique);
        queryMap.put("mainId",startSaleListMainId);
        queryMap.put("pageSize",orderSyncTaskPageSize);

        return saleListMainMapper.querySaleListMainPage(queryMap);
    }

    /**
     * 查询子订单
     * @param mainUniqueList
     * @return
     */
    private List<CanyinSaleListEntity> selectCanyinSaleList(List<String> mainUniqueList) {
        LambdaQueryWrapper<CanyinSaleListEntity> saleListWrapper = new LambdaQueryWrapper<>();
        saleListWrapper.in(CanyinSaleListEntity::getSaleListMainUnique,mainUniqueList);
        return saleListMapper.selectList(saleListWrapper);
    }

    /**
     * 查询订单详情
     * @param saleListUniqueList
     * @return
     */
    private List<CanyinSaleListDetailEntity> selectCanyinSaleListDetail(List<String> saleListUniqueList) {
        LambdaQueryWrapper<CanyinSaleListDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(CanyinSaleListDetailEntity::getSaleListUnique,saleListUniqueList);
        return saleListDetailMapper.selectList(detailWrapper);
    }

    /**
     * 查询支付信息
     * @param mainUniqueList
     * @return
     */
    private List<CanyinSaleListMainPayDetailEntity> selectMainPayDetail(List<String> mainUniqueList) {
        LambdaQueryWrapper<CanyinSaleListMainPayDetailEntity> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.in(CanyinSaleListMainPayDetailEntity::getSaleListMainUnique,mainUniqueList);
        return mainPayDetailMapper.selectList(mainWrapper);
    }

    /**
     * 订单同步
     * @return
     */
    private List<SaveSaleList> syncSaleList(QueryBindShopDto shop,List<CanyinSaleListMainEntity> saleListMainList,List<CanyinSaleListEntity> saleList,List<CanyinSaleListDetailEntity> detailList,List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        List<SaveSaleList> saveList = new ArrayList<>();
        for (CanyinSaleListMainEntity main : saleListMainList) {
            List<CanyinSaleListMainPayDetailEntity> dList = mainPayDetailList.stream().filter(d -> main.getSaleListMainUnique().equals(d.getSaleListMainUnique())).collect(Collectors.toList());
            saveList.add(convertSaveList(shop,main,saleList,detailList,dList));
        }

        //参数构建
        SaveSaleListParams params = new SaveSaleListParams();
        params.setSaveList(saveList);
        saleListFacade.saveSaleList(params);

        return saveList;
    }

    /**
     * 订单数据转换
     */
    private SaveSaleList convertSaveList(QueryBindShopDto shop,CanyinSaleListMainEntity main,List<CanyinSaleListEntity> saleList,List<CanyinSaleListDetailEntity> detailList,List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        SaveSaleList saveSaleList = new SaveSaleList();

        List<SaleListPayDetail> payDetailList = convertTaxSaleListPayDetailLsit(main, mainPayDetailList);
        saveSaleList.setSaleListPayDetailList(payDetailList);
        saveSaleList.setSaleListDetailList(convertTaxSaleListDetail(main,saleList,detailList));
        saveSaleList.setSaleList(convertTaxSaleList(shop,main,payDetailList));

        return saveSaleList;
    }

    /**
     * 餐饮订单信息转换
     * @param shop
     * @param main
     * @return
     */
    private SaleList convertTaxSaleList(QueryBindShopDto shop,CanyinSaleListMainEntity main,List<SaleListPayDetail> payDetailList) {
        //key：餐饮sale_type，value:纳统sale_type
        Map<String,Integer> saleTypeMap = new HashMap<>();
        saleTypeMap.put("1",21);
        saleTypeMap.put("2",22);
        saleTypeMap.put("3",23);

        //key：餐饮sale_list_state,value：纳统sale_list_state
        Map<String,Integer> saleListStateMap = new HashMap<>();
        saleListStateMap.put("1",21);
        saleListStateMap.put("2",22);
        saleListStateMap.put("3",23);

//        餐饮支付方式(31现金，32支付宝，33微信，34银行卡，35储值卡，36美团，37饿了么，38混合支付，39金圈支付，40积分兑换，41百货豆，42金圈支付，43金圈支付，44金圈聚合码)
        //key:餐饮sale_list_payment,value：纳统sale_list_payment
        Map<String,Integer> saleListPaymentMap = new HashMap<>();
        saleListPaymentMap.put("1",31);
        saleListPaymentMap.put("2",32);
        saleListPaymentMap.put("3",33);
        saleListPaymentMap.put("4",34);
        saleListPaymentMap.put("5",35);
        saleListPaymentMap.put("6",36);
        saleListPaymentMap.put("7",37);
        saleListPaymentMap.put("8",38);
        saleListPaymentMap.put("9",39);
        saleListPaymentMap.put("10",40);
        saleListPaymentMap.put("11",41);
        saleListPaymentMap.put("12",42);
        saleListPaymentMap.put("13",43);
        saleListPaymentMap.put("14",44);

        //线上支付金额
        BigDecimal onlinePay = payDetailList.stream().filter(p -> p.getServerType() != 1 && p.getServerType() != 6).map(SaleListPayDetail::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

        //纳统sale_list
        SaleList sl = new SaleList();
        sl.setCompanyId(shop.getCompanyId());
        sl.setShopUnique(shop.getShopUnique());
        sl.setShopName(shop.getShopName());
        sl.setSaleType(saleTypeMap.get(main.getSaleType()));
        sl.setSaleListUnique(main.getSaleListMainUnique());
        sl.setSaleListName("");
        sl.setSaleListTotal(main.getSaleListTotal());
        sl.setSaleListActuallyReceived(main.getSaleListActuallyReceived());
        BigDecimal serviceFee = NumberUtil.div(NumberUtil.mul(new BigDecimal(String.valueOf(onlinePay)), new BigDecimal("0.0038")), BigDecimal.ONE, 2, RoundingMode.CEILING);
        sl.setSaleListServiceFee(serviceFee);
        sl.setTradeNo(main.getTradeNo());
        sl.setPayTime(main.getPayDatetime());
        sl.setSaleListDatetime(main.getCreateTime());
        sl.setSaleListState(saleListStateMap.get(main.getSaleListState()));
        sl.setSaleListPayment(saleListPaymentMap.get(main.getSaleListPayment()));
        sl.setCouponAmount(BigDecimal.ZERO);
        sl.setShopCouponId(0);

        return sl;
    }

    /**
     * 餐饮订单详情转换
     * @return
     */
    private List<SaleListDetail> convertTaxSaleListDetail(CanyinSaleListMainEntity main,List<CanyinSaleListEntity> saleList,List<CanyinSaleListDetailEntity> detailList) {
        List<SaleListDetail> taxDetailList = new ArrayList<>();

        //筛选子订单
        List<CanyinSaleListEntity> sList = saleList.stream().filter(s -> main.getSaleListMainUnique().equals(s.getSaleListMainUnique())).collect(Collectors.toList());
        List<String> saleListUniqueList = sList.stream().map(CanyinSaleListEntity::getSaleListUnique).collect(Collectors.toList());
        //筛选订单详情
        List<CanyinSaleListDetailEntity> dList = detailList.stream().filter(d -> saleListUniqueList.contains(d.getSaleListUnique())).collect(Collectors.toList());

        for (CanyinSaleListDetailEntity d : dList) {
            SaleListDetail sld = new SaleListDetail();
            sld.setSaleListDetailId(d.getId());
            sld.setSaleListUnique(main.getSaleListMainUnique());
            sld.setGoodsId(d.getGoodsId());
            sld.setGoodsBarcode(d.getGoodsBarcode());
            sld.setGoodsName(d.getGoodsName());
            sld.setSaleListDetailCount(d.getSaleListDetailCount());
            sld.setSaleListDetailPrice(d.getSaleListDetailPrice());
            sld.setSaleListDetailSubtotal(d.getSaleListDetailSubtotal());

            taxDetailList.add(sld);
        }

        return taxDetailList;
    }

    /**
     * 餐饮支付详情转换
     * @return
     */
    private List<SaleListPayDetail> convertTaxSaleListPayDetailLsit(CanyinSaleListMainEntity main,List<CanyinSaleListMainPayDetailEntity> mainPayDetailList) {
        List<SaleListPayDetail> taxPayDetailList = new ArrayList<>();

        //key:餐饮pay_menthod,value:纳统payMethod
        Map<Integer,Integer> payMethodMap = new HashMap<>();
        payMethodMap.put(1,31);
        payMethodMap.put(2,32);
        payMethodMap.put(3,33);
        payMethodMap.put(4,34);
        payMethodMap.put(5,35);
        payMethodMap.put(6,36);
        payMethodMap.put(7,37);
        payMethodMap.put(8,38);
        payMethodMap.put(9,39);

        //key:餐饮server_type,value:纳统server_type
        Map<Integer,Integer> serverTypeMap = new HashMap<>();
        serverTypeMap.put(1,31);
        serverTypeMap.put(2,32);
        serverTypeMap.put(3,33);
        serverTypeMap.put(4,34);
        serverTypeMap.put(5,35);
        serverTypeMap.put(6,36);

        for (CanyinSaleListMainPayDetailEntity mp : mainPayDetailList) {
            SaleListPayDetail slp = new SaleListPayDetail();
            slp.setSaleListUnique(main.getSaleListMainUnique());
            slp.setPayMethod(payMethodMap.get(mp.getPayMethod()));
            slp.setPayMoney(mp.getPayMoney());
            slp.setServerType(serverTypeMap.get(mp.getServerType()));
            slp.setMchId(mp.getMchId());
            slp.setPayTime(mp.getCreateTime());

            taxPayDetailList.add(slp);
        }

        return taxPayDetailList;
    }

    /**
     * 转换退款数据
     * @param shop
     * @param jobParam
     * @param companyReturnList
     * @return
     */
    private Map<String,BigDecimal> convertReturnList(QueryBindShopDto shop,String jobParam,List<SyncReturnList> companyReturnList) {
        Map<String,BigDecimal> map = new HashMap<>();
        //在线支付退款金额
        map.put("onlineMoney",BigDecimal.ZERO);
        //退款退回的服务费
        map.put("returnServiceFee",BigDecimal.ZERO);

        //查询退款单
        Map<String, String> dateMap = handleDate(jobParam);
        LambdaQueryWrapper<CanyinRetListEntity> retListWrapper = new LambdaQueryWrapper<>();
        retListWrapper.eq(CanyinRetListEntity::getShopUnique,shop.getShopUnique());
        retListWrapper.eq(CanyinRetListEntity::getRetListStatus,"3"); //已完成退款
        retListWrapper.ge(CanyinRetListEntity::getRetbackTime,dateMap.get("dateStart"));
        retListWrapper.lt(CanyinRetListEntity::getRetbackTime,dateMap.get("dateEnd"));
        List<CanyinRetListEntity> retList = retListMapper.selectList(retListWrapper);

        if (ObjectUtil.isEmpty(retList)) return map;

        //退款单号
        List<String> retListUniqueList = retList.stream().map(CanyinRetListEntity::getRetListUnique).collect(Collectors.toList());

        //退款支付信息
        LambdaQueryWrapper<CanyinRetListPayDetailEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.in(CanyinRetListPayDetailEntity::getRetListUnique,retListUniqueList);
        List<CanyinRetListPayDetailEntity> payList = retListPayDetailMapper.selectList(payWrapper);

        //线上退款
        BigDecimal onlineMoney = payList.stream().filter(p -> !"1".equals(p.getServerType()) && !"6".equals(p.getServerType())).map(CanyinRetListPayDetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal returnServiceFee = BigDecimal.ZERO;

        for (CanyinRetListEntity rt : retList) {
            List<CanyinRetListPayDetailEntity> rtpList = payList.stream().filter(p -> p.getRetListUnique().equals(rt.getRetListUnique())).collect(Collectors.toList());

            SyncReturnList sync = new SyncReturnList();
            ReturnListRemote returnListRemote = convertTaxReturnList(rt, shop, rtpList);
            returnServiceFee = NumberUtil.add(returnServiceFee,returnListRemote.getReturnSaleListServiceFee());
            sync.setReturnList(returnListRemote);
            sync.setReturnListDetailList(new ArrayList<>());
            sync.setPaydetailList(convertTaxReturnPayList(rtpList));

            companyReturnList.add(sync);
        }

        map.put("onlineMoney",onlineMoney);
        map.put("returnServiceFee",returnServiceFee);
        return map;
    }

    /**
     * 退款单转换
     * @param rt
     * @return
     */
    private ReturnListRemote convertTaxReturnList(CanyinRetListEntity rt,QueryBindShopDto shop,List<CanyinRetListPayDetailEntity> rtpList) {
        //key:餐饮ret_list_status,value:纳统ret_list_state
        Map<Integer,String> retStatusMap = new HashMap<>();
        retStatusMap.put(1,"11");
        retStatusMap.put(2,"12");
        retStatusMap.put(3,"13");
        retStatusMap.put(4,"14");
        retStatusMap.put(5,"15");

        ReturnListRemote remote = new ReturnListRemote();
        remote.setCompanyId(shop.getCompanyId());
        remote.setSaleListUnique(rt.getSaleListMainUnique());
        remote.setShopUnique(shop.getShopUnique());
        remote.setRetListDatetime(rt.getCreateTime());
        remote.setRetListState(retStatusMap.get(rt.getRetListStatus()));
        remote.setRetListHandlestate("3");
        remote.setRetMoneyType(null);
        remote.setRetListTotalMoney(rt.getRetListMoney());
        remote.setRetBackDatetime(rt.getRetbackTime());
        remote.setRetListUnique(rt.getRetListUnique());
        remote.setRetListReason(rt.getRetListReason());
        remote.setRetListDelfee(BigDecimal.ZERO);
        BigDecimal serviceFee = rtpList.stream().filter(p -> !"1".equals(p.getServerType()) && !"6".equals(p.getServerType())).map(CanyinRetListPayDetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        serviceFee = NumberUtil.div(NumberUtil.mul(serviceFee,new BigDecimal("0.0038")),BigDecimal.ONE,2,RoundingMode.HALF_UP);
        remote.setReturnSaleListServiceFee(serviceFee);
        remote.setShopName(shop.getShopName());

        return remote;
    }

    /**
     * 退款数据转换
     * @param rtpList
     * @return
     */
    private List<ReturnListPaydetailRemote> convertTaxReturnPayList(List<CanyinRetListPayDetailEntity> rtpList) {
        List<ReturnListPaydetailRemote> retList = new ArrayList<>();

        //key:餐饮pay_menthod,value:纳统payMethod
        Map<Integer,Integer> payMethodMap = new HashMap<>();
        payMethodMap.put(1,31);
        payMethodMap.put(2,32);
        payMethodMap.put(3,33);
        payMethodMap.put(4,34);
        payMethodMap.put(5,35);
        payMethodMap.put(6,36);
        payMethodMap.put(7,37);
        payMethodMap.put(8,38);
        payMethodMap.put(9,39);

        //key:餐饮server_type,value:纳统server_type
        Map<Integer,Integer> serverTypeMap = new HashMap<>();
        serverTypeMap.put(1,31);
        serverTypeMap.put(2,32);
        serverTypeMap.put(3,33);
        serverTypeMap.put(4,34);
        serverTypeMap.put(5,35);
        serverTypeMap.put(6,36);

        for (CanyinRetListPayDetailEntity r : rtpList) {
            ReturnListPaydetailRemote p = new ReturnListPaydetailRemote();
            p.setSaleListUnique(r.getSaleListPayUnique());
            p.setRetListUnique(r.getRetListUnique());
            p.setPayType(payMethodMap.get(r.getPayMethod()));
            p.setPayMoney(r.getPayMoney());
            p.setServiceType(serverTypeMap.get(r.getServerType()));
            p.setMchId("");

            retList.add(p);
        }

        return retList;
    }

    /**
     * 更新商户待结算金额
     */
    private void updateUnsettledAmount(Long shopUnique, Long companyId ,BigDecimal onlineMoney,Integer payType) {
        UpdateUnsettledAmountRemoteParams upd = new UpdateUnsettledAmountRemoteParams();
        upd.setShopUnique(shopUnique);
        upd.setBusId(0L);
        upd.setCompanyId(companyId);
        upd.setPayType(payType);
        upd.setOnlineMoney(onlineMoney);
        XxlJobHelper.log("payType:{},onlineMoney:{}",payType,onlineMoney);

        busShopBillFacade.updateUnsettledAmount(upd);
    }

    /**
     * 更新服务费
     * @param companyId
     * @param totalServiceFee
     * @param shop
     */
    private void updateServiceFee(Long companyId,BigDecimal totalServiceFee,QueryBindShopDto shop) {
        UpdateShopServiceFeeParams params = new UpdateShopServiceFeeParams();
        params.setServiceFee(totalServiceFee);
        params.setShopUnique(shop.getShopUnique());
        params.setCompanyId(companyId);
        busShopServiceFeeFacade.updateShopServiceFee(params);
    }

    /**
     * 保存公司退款单
     */
    private void syncCompanyReturnList(Long companyId,List<SyncReturnList> companyReturnList) {
        if (ObjectUtil.isEmpty(companyReturnList)) return;

        SyncReturnListParams params = new SyncReturnListParams();
        params.setReturnList(companyReturnList);
        params.setCompanyId(companyId);
        params.setBusinessType("canyin");

        returnListFacade.syncReturnList(params);
    }

}
