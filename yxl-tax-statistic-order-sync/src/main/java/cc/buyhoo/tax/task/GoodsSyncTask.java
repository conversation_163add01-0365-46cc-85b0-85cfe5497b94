package cc.buyhoo.tax.task;


import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.ThreadPoolUtils;
import cc.buyhoo.tax.dao.GoodsMapper;
import cc.buyhoo.tax.dao.ShopMapper;
import cc.buyhoo.tax.entity.GoodsEntity;
import cc.buyhoo.tax.entity.ShopEntity;
import cc.buyhoo.tax.facade.BusShopFacade;
import cc.buyhoo.tax.facade.GoodsFacade;
import cc.buyhoo.tax.facade.ShopFacade;
import cc.buyhoo.tax.facade.params.goodsList.Goods;
import cc.buyhoo.tax.facade.params.shop.Shop;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class GoodsSyncTask {

    @DubboReference
    private BusShopFacade busShopFacade;

    @DubboReference
    private GoodsFacade goodsFacade;

    @DubboReference
    private ShopFacade shopFacade;

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private ShopMapper shopMapper;

    private final ExecutorService pool = ThreadPoolUtils.newThreadPool();

    @XxlJob("goodsSyncJobHandler")
    public void goodsSync() {
        Date startTime = DateUtil.date();
        log.info("------定时任务同步商品信息：--------------------开始同步----------------------------");
        //查询所有的店铺
        Result<QueryBindShopResult> shopResult = busShopFacade.queryBindShop();
        if (shopResult.hasFail()) {
            XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
        }

        //店铺列表
        List<QueryBindShopDto> shopList = shopResult.getData().getShopList();

        //同步店铺信息
        pool.execute(() -> {
            Set<Long> shopUniques = shopList.stream().map(QueryBindShopDto::getShopUnique).collect(Collectors.toSet());;
            if (CollUtil.isNotEmpty(shopUniques) && shopUniques.size() > 0) {
                shopFacade.syncShop(queryShops(shopUniques));
            }
        });
        for (QueryBindShopDto shop : shopList) {
            List<Goods> goodsDtoList = queryGoods(shop.getShopUnique());
            if (CollUtil.isNotEmpty(goodsDtoList)) {
                goodsFacade.syncGoods(shop.getCompanyId(), shop.getShopUnique(), goodsDtoList);
            }
        }

        long millis = DateUtil.between(startTime, DateUtil.date(), DateUnit.MS);
        log.info("--------定时任务同步商品信息------------------结束同步--------------------------耗时：{}--", DateUtil.formatBetween(millis, BetweenFormatter.Level.MILLISECOND));
    }

    private List<Goods> queryGoods(long shopUnique) {
        LambdaQueryWrapper<GoodsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoodsEntity::getShopUnique, shopUnique);
        List<GoodsEntity> list = goodsMapper.selectList(queryWrapper);
        List<Goods> goodsList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            goodsList = list.stream().map(v -> {
                Goods goods = new Goods();
                BeanUtil.copyProperties(v, goods);
                return goods;
            }).collect(Collectors.toList());
        }
        return goodsList;
    }

    private List<Shop> queryShops(Set<Long> shopUniques) {
        List<ShopEntity> shopEntityList = shopMapper.selectList(new LambdaQueryWrapper<ShopEntity>().in(ShopEntity::getShopUnique, shopUniques));
        List<Shop> shops = shopEntityList.stream().map(v -> {
            Shop shop = new Shop();
            BeanUtil.copyProperties(v, shop);
            return shop;
        }).collect(Collectors.toList());
        return shops;
    }
}
