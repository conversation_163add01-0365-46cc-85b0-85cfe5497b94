package cc.buyhoo.tax.task;

import cc.buyhoo.tax.facade.BusInventoryOrderFacade;
import cc.buyhoo.tax.facade.params.busInventoryOrder.SaveInventoryOrderRemoteParams;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

@Component
public abstract class AbstractSyncTaskHelper {

    @DubboReference
    private BusInventoryOrderFacade busInventoryOrderFacade;

    /**
     * 入库单
     * @param companyId
     * @param inventoryType 入库单类型:1收银订单同步2餐饮订单同步(仅统计数据使用)3手动录入
     * @return
     */
    public Long saveInventoryOrder(Long companyId,Integer inventoryType,BigDecimal cashMoney,BigDecimal onlineMoney) {
        SaveInventoryOrderRemoteParams params = new SaveInventoryOrderRemoteParams();
        params.setOrderNo(StringUtils.join("CG", DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        params.setCompanyId(companyId);
        params.setCashMoney(cashMoney);
        params.setOnlineMoney(onlineMoney);
        params.setTotalMoney(NumberUtil.add(cashMoney,onlineMoney));
        params.setInventoryType(inventoryType);

        return busInventoryOrderFacade.saveInventoryOrder(params);
    }

}
