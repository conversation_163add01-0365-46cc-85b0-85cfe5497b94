<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yxl-statistic</artifactId>
        <groupId>cc.buyhoo.tax</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>cc.buyhoo.tax</groupId>
    <artifactId>yxl-tax-statistic-order-sync</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <yxl.version>2.1.0</yxl.version>
        <tax-statistic.version>1.0.0</tax-statistic.version>
        <mysql.version>5.1.37</mysql.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-core</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-datasource</artifactId>
            <version>${yxl.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mysql</groupId>
                    <artifactId>mysql-connector-j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-dubbo</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-job</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-web</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.tax</groupId>
            <artifactId>yxl-tax-statistic-dubbo-api</artifactId>
            <version>${tax-statistic.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>