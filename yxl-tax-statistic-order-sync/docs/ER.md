# 税务统计订单同步系统 - 数据库ER图

## 1. 概述

本文档描述了税务统计订单同步系统的数据库表结构及其关系。系统主要包含百货订单、餐饮订单、商品信息、店铺信息等核心业务实体，通过ER图展示各表之间的关联关系。

> 📖 **相关文档**: 详细的业务流程和代码说明请参考 [业务流程文档](BIZ.md)

## 2. 核心业务域

### 2.1 百货业务域
- **销售订单管理**: 销售订单主表、详情表、支付详情表
- **退货管理**: 退货单主表、详情表、支付详情表
- **优惠券管理**: 优惠券配置、用户优惠券关联

### 2.2 餐饮业务域
- **餐饮订单管理**: 主订单表、子订单表、详情表、支付详情表
- **餐饮退货管理**: 退货单主表、详情表、支付详情表

### 2.3 基础数据域
- **商品管理**: 商品基础信息
- **店铺管理**: 店铺基础信息

## 3. 数据库ER图

### 3.1 整体ER图

```mermaid
erDiagram
    %% 店铺和商品基础数据
    shops {
        string shopName "店铺名称"
        string shopUnique "店铺唯一标识"
        string shopAddressDetail "店铺详细地址"
        integer shopType "店铺类型"
        string shopPhone "店铺电话"
    }
    
    goods {
        integer goodsId PK "商品编号"
        bigint shopUnique "店铺唯一标识"
        string goodsBarcode "商品条形码"
        string goodsBrand "商品品牌"
        string goodsName "商品名称"
        string goodsAlias "商品别名"
        decimal goodsInPrice "商品进价"
        decimal goodsSalePrice "商品售价"
        string goodsStandard "商品规格"
        string goodsUnit "商品计价单位"
        decimal goodsWebSalePrice "商品线上售价"
    }
    
    %% 百货订单相关表
    sale_list {
        integer saleListId PK "订单ID"
        bigint saleListUnique UK "订单编号"
        bigint shopUnique "店铺唯一标识"
        datetime saleListDatetime "销售单日期"
        decimal saleListTotal "应收金额"
        decimal saleListPur "订单进货价"
        integer saleListTotalcount "商品总数量"
        string cusUnique "消费者唯一编号"
        integer saleType "订单类型"
        string saleListName "收货人姓名"
        string saleListPhone "收货人联系电话"
        integer saleListState "订单状态"
        string saleListPayment "支付方式"
        decimal saleListActuallyReceived "实际收款金额"
        string tradeNo "交易流水号"
        integer shopCouponId "优惠券ID"
        decimal couponAmount "优惠券金额"
    }
    
    sale_list_detail {
        integer saleListDetailId PK "销售清单ID"
        bigint saleListUnique "销售单唯一标识"
        string goodsBarcode "商品条形码"
        string goodsName "商品名称"
        string goodsPicturepath "商品图片路径"
        decimal saleListDetailCount "商品数量"
        decimal saleListDetailPrice "商品购买价格"
        decimal saleListDetailSubtotal "金额小计"
        bigint goodsId "商品ID"
        decimal goodsPurprice "商品进价"
        decimal commissionTotal "提成小计"
        decimal goodsOldPrice "商品原价"
    }
    
    sale_list_pay_detail {
        integer saleListPayDetailId PK "支付详情ID"
        bigint saleListUnique "销售单唯一标识"
        integer payMethod "支付方式"
        decimal payMoney "支付金额"
        integer serverType "支付服务端"
        string mchId "收款店铺"
    }
    
    %% 百货退货相关表
    return_list {
        integer id PK "退货单号"
        string saleListUnique "销售订单编号"
        string shopUnique "退款店铺编号"
        datetime retListDatetime "退货日期"
        decimal retListTotal "退货总金额"
        decimal retListCount "退货总数量"
        string retListState "退款状态"
        string retListHandlestate "退货申请受理状态"
        string retListRemarks "备注信息"
        integer staffId "操作员工编号"
        string macId "退货机器macid"
        integer retOrigin "退货来源"
        integer retMoneyType "退款方式"
        decimal retListTotalMoney "实际退款金额"
        string retListUnique "退款申请单号"
    }
    
    return_list_detail {
        integer retListDetailId PK "退货详情ID"
        string saleListUnique "退货单唯一标识符"
        string goodsBarcode "商品条形码"
        string goodsName "商品名称"
        decimal retListDetailCount "退货数量"
        decimal retListDetailPrice "退货价格"
        integer handleWay "退回货物处理方式"
        decimal retListOriginPrice "退货商品销售原价"
        string retListUnique "退款单号"
        integer rsaleListDetailId "订单详情ID"
    }
    
    return_list_paydetail {
        integer id PK "退款支付详情ID"
        string saleListUnique "订单单号"
        string retListUnique "退货单号"
        integer payType "退款收款方式"
        decimal payMoney "退款金额"
        integer serviceType "支付服务端"
        string mchId "退款账号"
    }

    %% 优惠券相关表
    shop_coupon {
        integer shopCouponId PK "优惠券ID"
        bigint shopUnique "店铺编号"
        bigint designatedShopUnique "指定店铺编号"
        integer couponType "优惠券类型"
        datetime startTime "开始时间"
        datetime endTime "结束时间"
        datetime createTime "创建时间"
        datetime updateTime "修改时间"
        decimal meetAmount "满足金额"
        decimal couponAmount "优惠金额"
        integer type "商品类型限制"
        integer deleteStatus "删除状态"
    }

    shop_coupon_cus {
        integer shopCouponCusId PK "用户优惠券ID"
        integer shopCouponId "优惠券ID"
        bigint cusUnique "用户编号"
        integer useStatus "使用状态"
        datetime createTime "领取时间"
        datetime useTime "使用时间"
        string couponCode "兑换码"
        string useShopUnique "使用店铺编号"
        string orderNo "第三方单号"
    }

    %% 餐饮订单相关表
    canyin_sale_list_main {
        bigint id PK "主订单ID"
        bigint shopUnique "店铺编号"
        string saleListMainUnique UK "订单编号"
        bigint saleListCashier "员工ID"
        bigint cusUnique "会员编号"
        bigint tableId "桌号"
        string pickupCode "取餐码"
        decimal saleListTotal "订单应付总金额"
        decimal saleListActuallyReceived "订单实付金额"
        decimal saleListIgnoreMoney "抹零金额"
        decimal saleListDiscountMoney "优惠金额"
        string saleType "订单类型"
        string saleListHandlestate "订单状态"
        integer shippingMethod "配送方式"
        integer saleListState "订单付款状态"
        string saleListRemarks "订单备注"
        decimal saleListPur "商品总进价"
        string saleListPayment "支付方式"
        decimal saleListTotalCount "商品累计数量"
        datetime receiptDatetime "订单收货时间"
        datetime completeDatetime "订单完成时间"
        datetime payDatetime "支付完成时间"
        string tradeNo "交易流水号"
        bigint serialNumber "流水号"
        integer saleListKindcount "商品类别数量"
        integer dinersCount "就餐人数"
        string remark "下单备注"
        integer paySetting "付款设置"
        bigint createId "下单人ID"
        datetime createTime "订单创建时间"
        datetime modifyTime "更新时间"
        string createBy "下单人名称"
        integer operLock "操作锁定状态"
        integer staffId "员工ID"
    }

    canyin_sale_list {
        bigint id PK "子订单ID"
        string saleListMainUnique "主订单编号"
        string saleListUnique UK "子订单编号"
        bigint shopUnique "店铺编号"
        decimal saleListTotal "子订单应付金额"
        decimal saleListActuallyReceived "子订单实付金额"
        string saleListHandlestate "子订单状态"
        integer saleListState "子订单付款状态"
        datetime payDatetime "支付完成时间"
        string tradeNo "交易流水号"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
    }

    canyin_sale_list_detail {
        bigint id PK "餐饮订单详情ID"
        string saleListUnique "订单编号"
        string goodsBarcode "商品条码"
        string goodsName "商品名称"
        bigint goodsId "商品ID"
        decimal goodsSalePrice "商品销售原价"
        decimal saleListDetailPrice "商品售价"
        decimal packFee "打包费"
        decimal saleListDetailPurprice "商品进价"
        decimal saleListDetailCount "商品数量"
        decimal saleListDetailSubtotal "小计"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
        integer delFlag "删除标识"
    }

    canyin_sale_list_main_pay_detail {
        bigint id PK "餐饮主订单支付详情ID"
        string saleListMainUnique "订单编号"
        integer payMethod "付款方式"
        decimal payMoney "支付金额"
        integer serverType "收款方式"
        string mchId "收款账户"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
        string transIndex "三方支付流水号"
        string saleListPayUnique "支付订单号"
    }

    %% 餐饮退货相关表
    canyin_ret_list {
        bigint id PK "餐饮退货单ID"
        bigint shopUnique "店铺编号"
        string saleListMainUnique "主订单编号"
        string retListUnique UK "退款单号"
        decimal retListTotal "退款总金额"
        decimal retListCount "退款总数量"
        string retListState "退款状态"
        string retListHandlestate "退款申请受理状态"
        string retListRemarks "备注信息"
        integer staffId "操作员工编号"
        datetime retListDatetime "退货日期"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
    }

    canyin_ret_list_detail {
        bigint id PK "餐饮退货详情ID"
        string retListUnique "退款单号"
        bigint goodsId "商品ID"
        string goodsBarcode "商品条码"
        decimal retCount "退款商品数量"
        decimal retPrice "退款商品金额"
        bigint rsaleListDetailId "订单详情ID"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
    }

    canyin_ret_list_pay_detail {
        bigint id PK "餐饮退货支付详情ID"
        string retListUnique "退款单号"
        string serverType "收款方式"
        string payMethod "付款方式"
        datetime createTime "创建时间"
        datetime modifyTime "更新时间"
        decimal payMoney "退款金额"
        string saleListPayUnique "支付订单编号"
    }

    %% 表关系定义
    shops ||--o{ goods : "店铺拥有商品"
    shops ||--o{ sale_list : "店铺销售订单"
    shops ||--o{ canyin_sale_list_main : "店铺餐饮订单"
    shops ||--o{ shop_coupon : "店铺优惠券"

    goods ||--o{ sale_list_detail : "商品销售详情"
    goods ||--o{ canyin_sale_list_detail : "商品餐饮详情"

    sale_list ||--o{ sale_list_detail : "订单包含详情"
    sale_list ||--o{ sale_list_pay_detail : "订单支付详情"
    sale_list ||--o{ return_list : "订单可退货"

    sale_list_detail ||--o{ return_list_detail : "详情可退货"

    return_list ||--o{ return_list_detail : "退货单包含详情"
    return_list ||--o{ return_list_paydetail : "退货单支付详情"

    shop_coupon ||--o{ shop_coupon_cus : "优惠券用户关联"
    shop_coupon ||--o{ sale_list : "优惠券使用"

    canyin_sale_list_main ||--o{ canyin_sale_list : "主订单包含子订单"
    canyin_sale_list_main ||--o{ canyin_sale_list_main_pay_detail : "主订单支付详情"
    canyin_sale_list_main ||--o{ canyin_ret_list : "主订单可退货"

    canyin_sale_list ||--o{ canyin_sale_list_detail : "子订单包含详情"

    canyin_sale_list_detail ||--o{ canyin_ret_list_detail : "餐饮详情可退货"

    canyin_ret_list ||--o{ canyin_ret_list_detail : "餐饮退货单包含详情"
    canyin_ret_list ||--o{ canyin_ret_list_pay_detail : "餐饮退货单支付详情"
```

### 3.2 百货业务域ER图

```mermaid
erDiagram
    shops ||--o{ sale_list : "店铺销售"
    sale_list ||--o{ sale_list_detail : "订单详情"
    sale_list ||--o{ sale_list_pay_detail : "支付详情"
    sale_list ||--o{ return_list : "退货关联"
    return_list ||--o{ return_list_detail : "退货详情"
    return_list ||--o{ return_list_paydetail : "退款详情"
    goods ||--o{ sale_list_detail : "商品销售"
    shop_coupon ||--o{ sale_list : "优惠券使用"
    shop_coupon ||--o{ shop_coupon_cus : "用户领取"
```

### 3.3 餐饮业务域ER图

```mermaid
erDiagram
    shops ||--o{ canyin_sale_list_main : "店铺餐饮订单"
    canyin_sale_list_main ||--o{ canyin_sale_list : "主子订单"
    canyin_sale_list_main ||--o{ canyin_sale_list_main_pay_detail : "主订单支付"
    canyin_sale_list ||--o{ canyin_sale_list_detail : "子订单详情"
    canyin_sale_list_main ||--o{ canyin_ret_list : "餐饮退货"
    canyin_ret_list ||--o{ canyin_ret_list_detail : "退货详情"
    canyin_ret_list ||--o{ canyin_ret_list_pay_detail : "退款详情"
    goods ||--o{ canyin_sale_list_detail : "商品销售"
```

## 4. 表结构详细说明

### 4.1 基础数据表

#### 4.1.1 店铺表 (shops)
- **用途**: 存储店铺基础信息
- **主键**: shopUnique (店铺唯一标识)
- **关键字段**:
  - shopName: 店铺名称
  - shopType: 店铺类型 (百货/餐饮)
  - shopAddressDetail: 店铺详细地址

#### 4.1.2 商品表 (goods)
- **用途**: 存储商品基础信息
- **主键**: goodsId (商品编号)
- **外键**: shopUnique → shops.shopUnique
- **关键字段**:
  - goodsBarcode: 商品条形码 (唯一标识)
  - goodsName: 商品名称
  - goodsInPrice: 商品进价
  - goodsSalePrice: 商品售价

### 4.2 百货业务表

#### 4.2.1 销售订单主表 (sale_list)
- **用途**: 存储百货销售订单主要信息
- **主键**: saleListId (自增ID)
- **唯一键**: saleListUnique (订单编号)
- **外键**:
  - shopUnique → shops.shopUnique
  - shopCouponId → shop_coupon.shopCouponId
- **关键字段**:
  - saleListState: 订单状态 (3=已支付完成)
  - saleType: 订单类型 (0=实体店,1=APP,2=小程序等)
  - saleListTotal: 应收金额
  - saleListActuallyReceived: 实际收款金额

#### 4.2.2 销售订单详情表 (sale_list_detail)
- **用途**: 存储订单商品详情信息
- **主键**: saleListDetailId (自增ID)
- **外键**:
  - saleListUnique → sale_list.saleListUnique
  - goodsId → goods.goodsId
- **关键字段**:
  - saleListDetailCount: 商品数量
  - saleListDetailPrice: 商品购买价格
  - saleListDetailSubtotal: 金额小计

#### 4.2.3 销售订单支付详情表 (sale_list_pay_detail)
- **用途**: 存储订单支付方式详情
- **主键**: saleListPayDetailId (自增ID)
- **外键**: saleListUnique → sale_list.saleListUnique
- **关键字段**:
  - payMethod: 支付方式 (1=现金,2=支付宝,3=微信等)
  - payMoney: 支付金额
  - serverType: 支付服务端类型

#### 4.2.4 退货单主表 (return_list)
- **用途**: 存储百货退货单主要信息
- **主键**: id (退货单号)
- **外键**:
  - saleListUnique → sale_list.saleListUnique
  - shopUnique → shops.shopUnique
- **关键字段**:
  - retListState: 退款状态 (1=未退款,2=已退款)
  - retListHandlestate: 受理状态 (1=未处理,2=已受理,3=受理完毕,4=驳回)
  - retListTotal: 退货总金额
  - retMoneyType: 退款方式

#### 4.2.5 退货单详情表 (return_list_detail)
- **用途**: 存储退货商品详情信息
- **主键**: retListDetailId (自增ID)
- **外键**:
  - saleListUnique → sale_list.saleListUnique
  - retListUnique → return_list.retListUnique
  - rsaleListDetailId → sale_list_detail.saleListDetailId
- **关键字段**:
  - retListDetailCount: 退货数量
  - retListDetailPrice: 退货价格
  - handleWay: 退回货物处理方式 (1=入库,2=报损,3=其他)

### 4.3 餐饮业务表

#### 4.3.1 餐饮主订单表 (canyin_sale_list_main)
- **用途**: 存储餐饮主订单信息
- **主键**: id (自增ID)
- **唯一键**: saleListMainUnique (主订单编号)
- **外键**: shopUnique → shops.shopUnique
- **关键字段**:
  - saleType: 订单类型 (1=堂食,2=打包,3=外卖)
  - saleListState: 付款状态 (1=未付款,2=已付款)
  - saleListHandlestate: 订单状态
  - tableId: 桌号 (堂食订单)
  - serialNumber: 流水号 (同桌客户标识)

#### 4.3.2 餐饮子订单表 (canyin_sale_list)
- **用途**: 存储餐饮子订单信息 (支持分单支付)
- **主键**: id (自增ID)
- **唯一键**: saleListUnique (子订单编号)
- **外键**:
  - saleListMainUnique → canyin_sale_list_main.saleListMainUnique
  - shopUnique → shops.shopUnique
- **关键字段**:
  - saleListTotal: 子订单应付金额
  - saleListActuallyReceived: 子订单实付金额
  - saleListState: 子订单付款状态

#### 4.3.3 餐饮订单详情表 (canyin_sale_list_detail)
- **用途**: 存储餐饮订单商品详情
- **主键**: id (自增ID)
- **外键**:
  - saleListUnique → canyin_sale_list.saleListUnique
  - goodsId → goods.goodsId
- **关键字段**:
  - saleListDetailPrice: 商品售价 (含规格、配菜、打包费)
  - packFee: 打包费
  - delFlag: 删除标识 (0=正常,1=删除)

### 4.4 优惠券相关表

#### 4.4.1 优惠券配置表 (shop_coupon)
- **用途**: 存储优惠券配置信息
- **主键**: shopCouponId (自增ID)
- **外键**: shopUnique → shops.shopUnique
- **关键字段**:
  - couponType: 优惠券类型 (1=平台通用,2=连锁店通用,3=指定店铺)
  - meetAmount: 满足金额 (满减条件)
  - couponAmount: 优惠金额
  - type: 商品类型限制 (1=全品类,2=仅限非折扣商品)

#### 4.4.2 用户优惠券关联表 (shop_coupon_cus)
- **用途**: 存储用户领取和使用优惠券记录
- **主键**: shopCouponCusId (自增ID)
- **外键**: shopCouponId → shop_coupon.shopCouponId
- **关键字段**:
  - cusUnique: 用户编号
  - useStatus: 使用状态 (0=未用,1=已使用)
  - couponCode: 兑换码
  - useShopUnique: 使用店铺编号

## 5. 业务规则与约束

### 5.1 数据完整性约束

#### 5.1.1 主键约束
- 所有表都有明确的主键定义
- 使用自增ID或业务唯一标识作为主键
- 确保主键的唯一性和非空性

#### 5.1.2 外键约束
- **店铺关联**: 所有业务表都通过 shopUnique 关联到 shops 表
- **订单关联**: 详情表通过订单编号关联到主表
- **商品关联**: 订单详情通过 goodsId 关联到 goods 表
- **优惠券关联**: 订单通过 shopCouponId 关联到优惠券表

#### 5.1.3 业务约束
- **订单状态**: 只同步已支付完成的订单 (sale_list_state=3)
- **时间范围**: 默认同步前一天的订单数据
- **数据去重**: 通过同步记录表防止重复同步
- **金额一致性**: 订单总金额 = 详情小计之和

### 5.2 数据同步规则

#### 5.2.1 百货订单同步
1. **同步范围**:
   - 销售订单 (sale_list)
   - 订单详情 (sale_list_detail)
   - 支付详情 (sale_list_pay_detail)
   - 退货单 (return_list)
   - 退货详情 (return_list_detail)

2. **同步条件**:
   - 订单状态为已支付完成 (sale_list_state=3)
   - 订单日期在指定时间范围内
   - 店铺已绑定税务系统

3. **同步顺序**:
   - 先同步主订单
   - 再同步订单详情
   - 最后同步支付详情

#### 5.2.2 餐饮订单同步
1. **同步范围**:
   - 主订单 (canyin_sale_list_main)
   - 子订单 (canyin_sale_list)
   - 订单详情 (canyin_sale_list_detail)
   - 支付详情 (canyin_sale_list_main_pay_detail)
   - 退货相关表

2. **同步特点**:
   - 支持主子订单结构
   - 支持分单支付
   - 支持桌号和流水号关联

#### 5.2.3 基础数据同步
1. **商品信息同步**:
   - 定时同步商品基础信息
   - 包含价格、规格、单位等信息
   - 确保订单详情中的商品信息完整

2. **店铺信息同步**:
   - 同步店铺基础信息
   - 包含店铺名称、地址、类型等
   - 作为订单数据的基础关联

### 5.3 数据质量保证

#### 5.3.1 数据验证
- **必填字段验证**: 确保关键字段非空
- **数据类型验证**: 确保字段类型正确
- **数值范围验证**: 确保金额、数量等字段合理
- **关联性验证**: 确保外键关联的数据存在

#### 5.3.2 异常处理
- **同步失败重试**: 支持失败重试机制
- **错误日志记录**: 详细记录同步过程中的错误
- **数据监控**: 监控订单数据异常和同步状态
- **手动修复**: 提供手动修复异常数据的机制

## 6. 索引设计建议

### 6.1 性能优化索引
```sql
-- 百货订单查询索引
CREATE INDEX idx_sale_list_shop_date_state ON sale_list(shop_unique, sale_list_datetime, sale_list_state);
CREATE INDEX idx_sale_list_unique ON sale_list(sale_list_unique);

-- 餐饮订单查询索引
CREATE INDEX idx_canyin_main_shop_date_state ON canyin_sale_list_main(shop_unique, pay_datetime, sale_list_state);
CREATE INDEX idx_canyin_main_unique ON canyin_sale_list_main(sale_list_main_unique);

-- 商品查询索引
CREATE INDEX idx_goods_shop_barcode ON goods(shop_unique, goods_barcode);
CREATE INDEX idx_goods_name ON goods(goods_name);

-- 店铺查询索引
CREATE INDEX idx_shops_unique ON shops(shop_unique);
CREATE INDEX idx_shops_type ON shops(shop_type);
```

### 6.2 外键关联索引
```sql
-- 订单详情关联索引
CREATE INDEX idx_sale_detail_unique ON sale_list_detail(sale_list_unique);
CREATE INDEX idx_sale_detail_goods ON sale_list_detail(goods_id);

-- 支付详情关联索引
CREATE INDEX idx_pay_detail_unique ON sale_list_pay_detail(sale_list_unique);

-- 退货关联索引
CREATE INDEX idx_return_sale_unique ON return_list(sale_list_unique);
CREATE INDEX idx_return_detail_unique ON return_list_detail(ret_list_unique);
```

## 7. 相关代码文件

### 7.1 实体类文件
- **百货订单相关**:
  - [`SaleListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListEntity.java) - 销售订单实体
  - [`SaleListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListDetailEntity.java) - 销售订单详情实体
  - [`SaleListPayDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListPayDetailEntity.java) - 销售订单支付详情实体
  - [`ReturnListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ReturnListEntity.java) - 退货单实体
  - [`ReturnListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ReturnListDetailEntity.java) - 退货单详情实体
  - [`ReturnListPaydetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ReturnListPaydetailEntity.java) - 退货单支付详情实体

- **餐饮订单相关**:
  - [`CanyinSaleListMainEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListMainEntity.java) - 餐饮主订单实体
  - [`CanyinSaleListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListEntity.java) - 餐饮子订单实体
  - [`CanyinSaleListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListDetailEntity.java) - 餐饮订单详情实体
  - [`CanyinSaleListMainPayDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListMainPayDetailEntity.java) - 餐饮主订单支付详情实体
  - [`CanyinRetListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinRetListEntity.java) - 餐饮退货单实体
  - [`CanyinRetListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinRetListDetailEntity.java) - 餐饮退货单详情实体
  - [`CanyinRetListPayDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinRetListPayDetailEntity.java) - 餐饮退货单支付详情实体

- **基础数据相关**:
  - [`GoodsEntity.java`](../src/main/java/cc/buyhoo/tax/entity/GoodsEntity.java) - 商品实体
  - [`ShopEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ShopEntity.java) - 店铺实体
  - [`ShopCouponEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ShopCouponEntity.java) - 优惠券实体
  - [`ShopCouponCusEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ShopCouponCusEntity.java) - 用户优惠券关联实体

### 7.2 数据访问层文件
- [`SaleListMapper.java`](../src/main/java/cc/buyhoo/tax/dao/SaleListMapper.java) - 销售订单数据访问
- [`CanyinSaleListMainMapper.java`](../src/main/java/cc/buyhoo/tax/dao/CanyinSaleListMainMapper.java) - 餐饮主订单数据访问
- [`GoodsMapper.java`](../src/main/java/cc/buyhoo/tax/dao/GoodsMapper.java) - 商品数据访问
- [`ReturnListMapper.java`](../src/main/java/cc/buyhoo/tax/dao/ReturnListMapper.java) - 退货单数据访问
- [`ShopMapper.java`](../src/main/java/cc/buyhoo/tax/dao/ShopMapper.java) - 店铺数据访问

### 7.3 业务处理文件
- [`OrderSyncCustomTask.java`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java) - 百货订单同步任务
- [`OrderSyncTask.java`](../src/main/java/cc/buyhoo/tax/task/OrderSyncTask.java) - 餐饮订单同步任务
- [`GoodsSyncTask.java`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java) - 商品同步任务

## 8. 总结

### 8.1 ER图特点
1. **业务域清晰**: 明确区分百货、餐饮、基础数据三个业务域
2. **关系完整**: 涵盖了订单、详情、支付、退货的完整业务流程
3. **扩展性好**: 支持多种订单类型和支付方式
4. **数据一致性**: 通过外键约束保证数据完整性

### 8.2 设计优势
1. **支持复杂业务**: 餐饮业务的主子订单结构，百货业务的多样化订单类型
2. **灵活的支付方式**: 支持多种支付方式和分单支付
3. **完整的退货流程**: 支持部分退货和多次退货
4. **优惠券体系**: 支持多种类型的优惠券和使用限制

### 8.3 维护建议
1. **定期检查**: 定期检查外键约束和数据一致性
2. **性能监控**: 监控查询性能，适时调整索引
3. **数据清理**: 定期清理过期的测试数据和无效记录
4. **文档更新**: 随着业务变化及时更新ER图和文档

---

*本文档基于项目当前版本编写，如有数据库结构变更请及时更新文档内容。*
