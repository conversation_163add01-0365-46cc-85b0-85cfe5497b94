# 税务统计订单同步系统业务流程文档

## 1. 项目概述

### 1.1 项目简介
`yxl-tax-statistic-order-sync` 是一个基于 Spring Boot 的微服务应用，主要负责从不同业务系统（百货收银系统、餐饮系统）同步订单数据到税务统计系统，为税务申报和统计分析提供数据支撑。

### 1.2 技术架构
- **框架**: Spring Boot 2.x
- **服务发现**: Nacos
- **RPC通信**: Dubbo
- **数据库**: MySQL
- **ORM**: MyBatis Plus
- **任务调度**: XXL-Job
- **配置管理**: Nacos Config

### 1.3 核心模块
- **订单同步模块**: 同步百货和餐饮订单数据
- **商品同步模块**: 同步商品基础信息
- **账单统计模块**: 统计服务费和结算数据
- **监控模块**: 订单数据监控和异常处理

## 2. 核心业务流程

### 2.0 整体数据流向

```mermaid
graph LR
    A[百货收银系统] --> D[订单同步服务]
    B[餐饮收银系统] --> D
    C[商品管理系统] --> D
    D --> E[税务统计系统]
    E --> F[税务申报]
    E --> G[财务报表]
    E --> H[数据分析]

    subgraph "同步任务"
        D1[百货订单同步]
        D2[餐饮订单同步]
        D3[商品信息同步]
        D4[账单统计]
        D5[订单监控]
    end

    D --> D1
    D --> D2
    D --> D3
    D --> D4
    D --> D5
```

### 2.1 百货订单同步流程

#### 2.1.1 主要任务类
- **OrderSyncCustomTask**: 百货订单定时同步任务
- **OrderSyncTask**: 百货订单测试同步任务

#### 2.1.2 业务流程
```mermaid
graph TD
    A[启动定时任务] --> B[查询分公司列表]
    B --> C[遍历分公司]
    C --> D[查询绑定店铺]
    D --> E[创建同步记录]
    E --> F[分页查询销售订单]
    F --> G[查询订单详情]
    G --> H[查询支付详情]
    H --> I[数据转换]
    I --> J[同步到税务系统]
    J --> K[处理退款单]
    K --> L[生成发票]
    L --> M[保存采购统计]
    M --> N[更新同步记录]
```

#### 2.1.3 详细业务逻辑
1. **分公司查询**: 通过 `SysCompanyFacade.queryBranchCompany()` 获取所有分公司
2. **店铺绑定查询**: 通过 `BusShopFacade.queryBindShop()` 获取绑定的店铺列表
3. **同步记录管理**: 创建和更新同步记录，防止重复同步
4. **分页数据查询**:
   - 查询销售订单: `handleQuerySaleList()`
   - 查询订单详情: `selectSaleListDetail()`
   - 查询支付详情: `selectSaleListPayDetail()`
   - 查询详情汇总: `selectSaleListDetailTotal()`
5. **数据转换**: 将原始订单数据转换为税务系统格式
6. **远程调用**: 通过 Dubbo 调用税务系统接口保存数据
7. **退款处理**: 处理退款单数据并同步
8. **发票生成**: 自动生成开票信息
9. **统计数据**: 保存采购统计数据用于报表

#### 2.1.4 核心代码链接
- 主任务入口: [`OrderSyncCustomTask.orderSync()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L96)
- 订单同步逻辑: [`OrderSyncCustomTask.syncSaleList()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L388)
- 数据转换: [`OrderSyncCustomTask.convertSaveList()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L202)
- 退款处理: [`OrderSyncCustomTask.convertShopReturnList()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L366)
- 发票生成: [`OrderSyncCustomTask.syncShopInvoice()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L524)

#### 2.1.5 涉及的数据表
- **sale_list**: 销售订单主表 - 存储订单基本信息
- **sale_list_detail**: 销售订单详情表 - 存储商品明细信息
- **sale_list_pay_detail**: 销售订单支付详情表 - 存储支付方式和金额
- **return_list**: 退货单主表 - 存储退货基本信息
- **return_list_detail**: 退货单详情表 - 存储退货商品明细
- **return_list_paydetail**: 退货单支付详情表 - 存储退款支付信息
- **shop_coupon**: 优惠券表 - 存储优惠券信息
- **shop_coupon_cus**: 优惠券使用记录表

#### 2.1.6 支付方式映射
- **1**: 现金支付
- **2**: 支付宝
- **3**: 微信支付
- **4**: 银行卡
- **5**: 储值卡
- **6**: 美团外卖
- **7**: 饿了么外卖
- **9**: 免密支付
- **10**: 积分兑换
- **11**: 百货豆
- **12**: 拉卡拉
- **13**: 易通支付
- **15**: 银联
- **16**: 合利宝

### 2.2 餐饮订单同步流程

#### 2.2.1 主要任务类
- **CanyinOrderSyncCustomTask**: 餐饮订单定时同步任务
- **CanyinOrderSyncTask**: 餐饮订单测试同步任务

#### 2.2.2 业务流程
```mermaid
graph TD
    A[启动定时任务] --> B[查询分公司列表]
    B --> C[遍历分公司]
    C --> D[查询绑定店铺]
    D --> E[创建同步记录]
    E --> F[分页查询主订单]
    F --> G[查询子订单]
    G --> H[查询订单详情]
    H --> I[查询支付详情]
    I --> J[数据转换]
    J --> K[同步到税务系统]
    K --> L[处理退款单]
    L --> M[保存采购统计]
    M --> N[更新同步记录]
```

#### 2.2.3 餐饮业务特点
1. **主子订单结构**: 餐饮系统采用主子订单模式
   - 主订单(`canyin_sale_list_main`): 记录整体订单信息
   - 子订单(`canyin_sale_list`): 记录具体商品订单
2. **订单类型映射**:
   - **1**: 堂食订单 → 税务系统类型 21
   - **2**: 打包订单 → 税务系统类型 22
   - **3**: 外卖订单 → 税务系统类型 23
3. **服务费计算**: 餐饮订单需要计算平台服务费
4. **退款处理**: 支持部分退款和整单退款

#### 2.2.4 核心代码链接
- 主任务入口: [`CanyinOrderSyncCustomTask.canyinOrderSyncJobHandler()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L128)
- 订单同步逻辑: [`CanyinOrderSyncCustomTask.syncSaleList()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L321)
- 数据转换: [`CanyinOrderSyncCustomTask.convertSaveList()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L339)
- 退款处理: [`CanyinOrderSyncCustomTask.convertReturnList()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L214)
- 餐饮订单转换: [`CanyinOrderSyncCustomTask.convertTaxSaleList()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L357)

#### 2.2.5 涉及的数据表
- **canyin_sale_list_main**: 餐饮主订单表 - 存储主订单信息
- **canyin_sale_list**: 餐饮子订单表 - 存储子订单信息
- **canyin_sale_list_detail**: 餐饮订单详情表 - 存储商品明细
- **canyin_sale_list_main_pay_detail**: 餐饮订单支付详情表 - 存储支付信息
- **canyin_ret_list**: 餐饮退货单表 - 存储退货信息
- **canyin_ret_list_pay_detail**: 餐饮退货单支付详情表 - 存储退款支付信息

### 2.3 商品同步流程

#### 2.3.1 主要任务类
- **GoodsSyncTask**: 商品信息同步任务

#### 2.3.2 业务流程
```mermaid
graph TD
    A[启动定时任务] --> B[查询绑定店铺]
    B --> C[同步店铺信息]
    C --> D[遍历店铺]
    D --> E[查询商品信息]
    E --> F[同步商品到税务系统]
```

#### 2.3.3 核心代码链接
- 主任务入口: [`GoodsSyncTask.goodsSync()`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java#L62)

#### 2.3.4 涉及的数据表
- **goods**: 商品信息表
- **shops**: 店铺信息表

### 2.4 账单统计流程

#### 2.4.1 主要任务类
- **BillStatisticTask**: 服务费和结算统计任务

#### 2.4.2 业务流程
```mermaid
graph TD
    A[启动定时任务] --> B[解析时间参数]
    B --> C[查询分公司列表]
    C --> D[遍历分公司]
    D --> E[异步统计账单]
```

#### 2.4.3 核心代码链接
- 主任务入口: [`BillStatisticTask.billStatistic()`](../src/main/java/cc/buyhoo/tax/task/BillStatisticTask.java#L38)

### 2.5 订单监控流程

#### 2.5.1 主要任务类
- **SaleListMonitorTask**: 订单监控任务

#### 2.5.2 业务流程
```mermaid
graph TD
    A[启动定时任务] --> B[设置监控时间范围]
    B --> C[查询分公司列表]
    C --> D[遍历分公司]
    D --> E[查询绑定店铺]
    E --> F[监控百货订单]
    F --> G[监控餐饮订单]
    G --> H[生成监控报告]
```

#### 2.5.3 核心代码链接
- 主任务入口: [`SaleListMonitorTask.saleListMonitor()`](../src/main/java/cc/buyhoo/tax/task/SaleListMonitorTask.java#L71)

### 2.6 公共基础服务

#### 2.6.1 抽象基类
- **AbstractSyncTaskHelper**: 同步任务辅助基类 - [`AbstractSyncTaskHelper.java`](../src/main/java/cc/buyhoo/tax/task/AbstractSyncTaskHelper.java)

#### 2.6.2 基类功能
1. **采购统计保存**: `saveInventoryOrder()` 方法
   - 生成采购订单号
   - 保存现金和线上支付金额统计
   - 支持不同业务类型标识
2. **业务类型定义**:
   - **1**: 收银订单同步
   - **2**: 餐饮订单同步（仅统计数据使用）
   - **3**: 手动录入

#### 2.6.3 数据清理任务
- **TruncateOrderDataTask**: 清空测试数据任务 - [`TruncateOrderDataTask.java`](../src/main/java/cc/buyhoo/tax/task/TruncateOrderDataTask.java)
- 用于测试环境清理纳统订单相关数据

## 3. 数据模型

### 3.1 核心实体类

#### 3.1.1 百货订单相关
- **SaleListEntity**: 销售订单实体 - [`SaleListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListEntity.java)
- **SaleListDetailEntity**: 销售订单详情实体 - [`SaleListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListDetailEntity.java)
- **SaleListPayDetailEntity**: 销售订单支付详情实体 - [`SaleListPayDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/SaleListPayDetailEntity.java)
- **ReturnListEntity**: 退货单实体 - [`ReturnListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ReturnListEntity.java)

#### 3.1.2 餐饮订单相关
- **CanyinSaleListMainEntity**: 餐饮主订单实体 - [`CanyinSaleListMainEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListMainEntity.java)
- **CanyinSaleListEntity**: 餐饮子订单实体 - [`CanyinSaleListEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListEntity.java)
- **CanyinSaleListDetailEntity**: 餐饮订单详情实体 - [`CanyinSaleListDetailEntity.java`](../src/main/java/cc/buyhoo/tax/entity/CanyinSaleListDetailEntity.java)

#### 3.1.3 基础数据相关
- **GoodsEntity**: 商品实体 - [`GoodsEntity.java`](../src/main/java/cc/buyhoo/tax/entity/GoodsEntity.java)
- **ShopEntity**: 店铺实体 - [`ShopEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ShopEntity.java)
- **ShopCouponEntity**: 优惠券实体 - [`ShopCouponEntity.java`](../src/main/java/cc/buyhoo/tax/entity/ShopCouponEntity.java)

### 3.2 数据访问层

#### 3.2.1 Mapper接口
- **SaleListMapper**: 销售订单数据访问 - [`SaleListMapper.java`](../src/main/java/cc/buyhoo/tax/dao/SaleListMapper.java)
- **CanyinSaleListMainMapper**: 餐饮主订单数据访问 - [`CanyinSaleListMainMapper.java`](../src/main/java/cc/buyhoo/tax/dao/CanyinSaleListMainMapper.java)
- **GoodsMapper**: 商品数据访问 - [`GoodsMapper.java`](../src/main/java/cc/buyhoo/tax/dao/GoodsMapper.java)
- **ReturnListMapper**: 退货单数据访问 - [`ReturnListMapper.java`](../src/main/java/cc/buyhoo/tax/dao/ReturnListMapper.java)
- **ShopMapper**: 店铺数据访问 - [`ShopMapper.java`](../src/main/java/cc/buyhoo/tax/dao/ShopMapper.java)

#### 3.2.2 MyBatis映射文件
- **SaleListMapper.xml**: 销售订单SQL映射 - [`SaleListMapper.xml`](../src/main/resources/mapper/SaleListMapper.xml)
- **CanyinSaleListMainMapper.xml**: 餐饮主订单SQL映射 - [`CanyinSaleListMainMapper.xml`](../src/main/resources/mapper/CanyinSaleListMainMapper.xml)
- **GoodsMapper.xml**: 商品SQL映射 - [`GoodsMapper.xml`](../src/main/resources/mapper/GoodsMapper.xml)
- **ReturnListMapper.xml**: 退货单SQL映射 - [`ReturnListMapper.xml`](../src/main/resources/mapper/ReturnListMapper.xml)

#### 3.2.3 参数和结果类
- **QueryOrderCountParams**: 订单统计查询参数 - [`QueryOrderCountParams.java`](../src/main/java/cc/buyhoo/tax/params/QueryOrderCountParams.java)
- **SelectBuyhooSaleListInfoDto**: 百货订单查询结果 - [`SelectBuyhooSaleListInfoDto.java`](../src/main/java/cc/buyhoo/tax/result/SelectBuyhooSaleListInfoDto.java)
- **SelectCanyinSaleListInfoDto**: 餐饮订单查询结果 - [`SelectCanyinSaleListInfoDto.java`](../src/main/java/cc/buyhoo/tax/result/SelectCanyinSaleListInfoDto.java)

## 4. 配置说明

### 4.1 应用配置
- **应用配置文件**: [`application.yml`](../src/main/resources/application.yml)
- **服务端口**: 14003
- **上下文路径**: /taxStatisticOrderSync

### 4.2 依赖配置
- **Maven配置**: [`pom.xml`](../pom.xml)
- **主要依赖**:
  - yxl-common-core: 核心工具类
  - yxl-common-dubbo: Dubbo服务调用
  - yxl-common-job: XXL-Job任务调度
  - yxl-common-datasource: 数据源配置
  - yxl-common-web: Web相关配置
  - yxl-tax-statistic-dubbo-api: 税务统计API接口

### 4.3 关键配置参数
- **order.sync.task.pageSize**: 订单同步分页大小，控制每次查询的数据量
- **nacos配置**: 服务注册发现和配置管理
- **dubbo配置**: RPC服务调用配置
- **数据库配置**: MySQL连接配置

## 5. 任务调度配置

### 5.1 XXL-Job任务列表
- **goodsSyncJobHandler**: 商品同步任务
- **orderSyncCustomJobHandler**: 百货订单同步任务
- **canyinOrderSyncJobHandler**: 餐饮订单同步任务
- **billStatisticJobHandler**: 账单统计任务
- **saleListMonitorJobHandler**: 订单监控任务
- **truncateOrderData**: 清空测试数据任务

### 5.2 任务执行时机
- 商品同步: 定时执行，同步商品基础信息
- 订单同步: 定时执行，同步前一天的订单数据
- 账单统计: 定时执行，统计服务费和结算数据
- 订单监控: 定时执行，监控订单数据异常

## 6. 业务规则与约束

### 6.1 数据同步规则
1. **时间范围**: 默认同步前一天的订单数据
2. **分页处理**: 使用分页查询避免大数据量问题，默认页大小可配置
3. **去重机制**: 通过同步记录表防止重复同步
4. **状态过滤**: 只同步已支付完成的订单（sale_list_state=3）
5. **数据完整性**: 确保订单、详情、支付信息的完整性

### 6.2 金额计算规则
1. **服务费计算**:
   - 百货订单: 按配置的费率计算
   - 餐饮订单: 按平台服务费计算
2. **支付方式分类**:
   - 现金支付: payMethod=1 且 serverType=1
   - 线上支付: 其他支付方式
3. **退款处理**: 退款金额需要扣减对应的服务费

### 6.3 数据转换规则
1. **订单类型映射**:
   - 百货订单保持原有类型
   - 餐饮订单类型需要转换（1→21, 2→22, 3→23）
2. **状态映射**: 不同系统的状态需要统一映射
3. **字段映射**: 确保字段类型和长度的兼容性

## 7. 异常处理

### 7.1 异常处理机制
- 使用 XXL-Job 的日志记录功能记录任务执行状态
- 通过 try-catch 捕获异常并记录详细错误信息
- 支持任务重试机制
- 异常时记录详细的堆栈信息便于排查

### 7.2 常见异常场景
- **网络连接异常**: Dubbo服务调用失败
- **数据格式错误**: 数据类型转换异常
- **业务逻辑异常**: 数据校验失败
- **第三方服务调用失败**: 远程服务不可用
- **数据库连接异常**: 数据库连接池耗尽
- **内存溢出**: 大数据量处理时内存不足

### 7.3 异常恢复策略
- **自动重试**: 网络异常等临时性问题自动重试
- **跳过处理**: 数据格式错误的记录跳过并记录日志
- **人工干预**: 严重异常需要人工处理
- **数据补偿**: 提供数据补偿机制

## 8. 监控与运维

### 8.1 日志配置
- **日志配置**: [`logback.xml`](../src/main/resources/logback.xml)
- **通用日志配置**: [`logback-common.xml`](../src/main/resources/logback-common.xml)

### 8.2 性能优化
- 使用分页查询避免大数据量查询
- 异步处理提高任务执行效率
- 合理设置数据库连接池参数

## 9. 部署说明

### 9.1 环境要求
- JDK 8+
- MySQL 5.7+
- Nacos 服务注册中心
- XXL-Job 调度中心

### 9.2 启动类
- **主启动类**: [`StatisticOrderSyncApplication.java`](../src/main/java/cc/buyhoo/tax/StatisticOrderSyncApplication.java)

### 9.3 启动配置说明
1. **注解配置**:
   - `@SpringBootApplication`: Spring Boot 主应用注解
   - `@MapperScan("cc.buyhoo.tax.dao")`: MyBatis Mapper 扫描
   - `@EnableDiscoveryClient`: 启用服务发现
   - `@EnableDubbo`: 启用 Dubbo 服务

2. **特殊配置**:
   - `System.setProperty("user.home", "dubboCache")`: 解决同一台服务器部署多个环境时 Dubbo 缓存文件重复问题

3. **启动横幅**: [`banner.txt`](../src/main/resources/banner.txt) - 显示应用启动信息

---

*本文档基于项目当前版本编写，如有更新请及时维护文档内容。*
